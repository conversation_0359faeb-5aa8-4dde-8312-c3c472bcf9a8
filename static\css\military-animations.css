/* Animations Militaires Avancées */

/* Effet de scan radar */
.radar-scan {
    position: relative;
    overflow: hidden;
}

.radar-scan::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(231, 76, 60, 0.1) 25%,
        rgba(231, 76, 60, 0.3) 50%,
        rgba(231, 76, 60, 0.1) 75%,
        transparent 100%);
    animation: radar-sweep 3s infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes radar-sweep {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Effet de pulsation militaire */
.military-pulse {
    animation: military-pulse 2s infinite;
}

@keyframes military-pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
    }
}

/* <PERSON>ffet de lueur militaire */
.military-glow {
    position: relative;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    box-shadow: 
        0 0 20px rgba(231, 76, 60, 0.3),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
}

.military-glow::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--accent-color), var(--accent-secondary));
    border-radius: inherit;
    z-index: -1;
    animation: glow-border 2s infinite;
}

@keyframes glow-border {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Effet de typing militaire */
.military-typing {
    overflow: hidden;
    border-right: 2px solid var(--accent-color);
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: var(--accent-color); }
}

/* Effets de glitch supprimés pour une interface plus stable */

/* Effet de chargement militaire */
.military-loading {
    position: relative;
    background: var(--bg-military);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.military-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(231, 76, 60, 0.4),
        transparent);
    animation: loading-sweep 1.5s infinite;
}

@keyframes loading-sweep {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Système de particules supprimé pour une interface plus propre */

/* Effet de slide militaire */
.military-slide-in {
    animation: slideInMilitary 0.6s ease-out;
}

@keyframes slideInMilitary {
    0% {
        transform: translateX(-100%) rotateY(-90deg);
        opacity: 0;
    }
    50% {
        transform: translateX(-50%) rotateY(-45deg);
        opacity: 0.5;
    }
    100% {
        transform: translateX(0) rotateY(0deg);
        opacity: 1;
    }
}

/* Effet de zoom militaire */
.military-zoom {
    animation: zoomMilitary 0.4s ease-out;
}

@keyframes zoomMilitary {
    0% {
        transform: scale(0) rotate(-180deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) rotate(-90deg);
        opacity: 0.7;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* Classes utilitaires pour les animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }
