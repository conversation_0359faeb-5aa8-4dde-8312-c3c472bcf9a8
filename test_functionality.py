#!/usr/bin/env python3
"""
Script de test pour vérifier toutes les fonctionnalités de StageManager
"""

import requests
import json
import time
import os

BASE_URL = "http://localhost:5000"

def test_api_endpoint(method, endpoint, data=None, expected_status=200):
    """Test un endpoint API"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method == 'GET':
            response = requests.get(url)
        elif method == 'POST':
            response = requests.post(url, json=data, headers={'Content-Type': 'application/json'})
        elif method == 'PUT':
            response = requests.put(url, json=data, headers={'Content-Type': 'application/json'})
        elif method == 'DELETE':
            response = requests.delete(url)
        
        print(f"✓ {method} {endpoint} - Status: {response.status_code}")
        
        if response.status_code == expected_status:
            return response.json() if response.content else None
        else:
            print(f"  ⚠️  Statut attendu: {expected_status}, reçu: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"✗ {method} {endpoint} - Erreur: {e}")
        return None

def test_basic_apis():
    """Test les APIs de base"""
    print("\n=== Test des APIs de base ===")
    
    # Test des stagiaires
    stagiaires = test_api_endpoint('GET', '/api/stagiaires')
    if stagiaires:
        print(f"  📊 {len(stagiaires)} stagiaires trouvés")
    
    # Test des stages
    stages = test_api_endpoint('GET', '/api/stages')
    if stages:
        print(f"  📊 {len(stages)} stages trouvés")
    
    # Test des promotions
    promotions = test_api_endpoint('GET', '/api/promotions')
    if promotions:
        print(f"  📊 {len(promotions)} promotions trouvées")
    
    # Test des types de stages
    types_stages = test_api_endpoint('GET', '/api/types-stages')
    if types_stages:
        print(f"  📊 {len(types_stages)} types de stages trouvés")
    
    # Test des documents
    documents = test_api_endpoint('GET', '/api/documents')
    if documents:
        print(f"  📊 {len(documents)} documents trouvés")
    
    # Test des statistiques
    stats = test_api_endpoint('GET', '/api/stats')
    if stats:
        print(f"  📊 Statistiques: {stats}")

def test_crud_operations():
    """Test les opérations CRUD"""
    print("\n=== Test des opérations CRUD ===")
    
    # Test création d'un stagiaire
    nouveau_stagiaire = {
        "nom": "Test",
        "prenom": "Utilisateur",
        "email": "<EMAIL>",
        "telephone": "0123456789",
        "niveau_etude": "bac+3",
        "specialite": "Test",
        "statut": "actif"
    }
    
    stagiaire_cree = test_api_endpoint('POST', '/api/stagiaires', nouveau_stagiaire, 201)
    if stagiaire_cree:
        stagiaire_id = stagiaire_cree['id']
        print(f"  ✓ Stagiaire créé avec ID: {stagiaire_id}")
        
        # Test modification
        stagiaire_modifie = {**nouveau_stagiaire, "telephone": "0987654321"}
        test_api_endpoint('PUT', f'/api/stagiaires/{stagiaire_id}', stagiaire_modifie)
        
        # Test suppression
        test_api_endpoint('DELETE', f'/api/stagiaires/{stagiaire_id}')
        print(f"  ✓ Stagiaire supprimé")

def test_document_generation():
    """Test la génération de documents"""
    print("\n=== Test de génération de documents ===")
    
    # Test génération convention PDF
    convention_data = {
        "type": "convention",
        "format": "pdf",
        "stagiaire_id": "stg-001",
        "stage_id": "stage-001"
    }
    
    doc_convention = test_api_endpoint('POST', '/api/documents', convention_data, 201)
    if doc_convention:
        print(f"  ✓ Convention générée: {doc_convention['nom']}")
    
    # Test génération attestation PDF
    attestation_data = {
        "type": "attestation",
        "format": "pdf",
        "stagiaire_id": "stg-002",
        "stage_id": "stage-002"
    }
    
    doc_attestation = test_api_endpoint('POST', '/api/documents', attestation_data, 201)
    if doc_attestation:
        print(f"  ✓ Attestation générée: {doc_attestation['nom']}")
    
    # Test génération liste Excel
    liste_data = {
        "type": "liste",
        "format": "excel",
        "promotion_id": "promo-001"
    }
    
    doc_liste = test_api_endpoint('POST', '/api/documents', liste_data, 201)
    if doc_liste:
        print(f"  ✓ Liste Excel générée: {doc_liste['nom']}")
    
    # Test génération liste PDF
    liste_pdf_data = {
        "type": "liste",
        "format": "pdf",
        "promotion_id": "promo-001"
    }
    
    doc_liste_pdf = test_api_endpoint('POST', '/api/documents', liste_pdf_data, 201)
    if doc_liste_pdf:
        print(f"  ✓ Liste PDF générée: {doc_liste_pdf['nom']}")

def test_search_functionality():
    """Test la fonctionnalité de recherche"""
    print("\n=== Test de la recherche ===")
    
    # Test recherche globale
    search_results = test_api_endpoint('GET', '/api/search?q=Jean')
    if search_results:
        results = search_results.get('results', [])
        print(f"  ✓ Recherche 'Jean': {len(results)} résultats")
        for result in results[:3]:  # Afficher les 3 premiers
            print(f"    - {result['type']}: {result['title']}")
    
    # Test recherche vide
    empty_search = test_api_endpoint('GET', '/api/search?q=')
    if empty_search:
        print(f"  ✓ Recherche vide: {len(empty_search.get('results', []))} résultats")

def test_export_functionality():
    """Test les fonctionnalités d'export"""
    print("\n=== Test des exports ===")
    
    # Test export stagiaires Excel
    try:
        response = requests.get(f"{BASE_URL}/api/export/stagiaires?format=excel")
        if response.status_code == 200:
            print(f"  ✓ Export stagiaires Excel: {len(response.content)} bytes")
        else:
            print(f"  ⚠️  Export stagiaires Excel échoué: {response.status_code}")
    except Exception as e:
        print(f"  ✗ Export stagiaires Excel erreur: {e}")
    
    # Test export stagiaires PDF
    try:
        response = requests.get(f"{BASE_URL}/api/export/stagiaires?format=pdf")
        if response.status_code == 200:
            print(f"  ✓ Export stagiaires PDF: {len(response.content)} bytes")
        else:
            print(f"  ⚠️  Export stagiaires PDF échoué: {response.status_code}")
    except Exception as e:
        print(f"  ✗ Export stagiaires PDF erreur: {e}")

def test_file_existence():
    """Vérifier l'existence des fichiers générés"""
    print("\n=== Vérification des fichiers ===")
    
    directories = ['uploads/conventions', 'uploads/attestations', 'uploads/listes']
    
    for directory in directories:
        if os.path.exists(directory):
            files = os.listdir(directory)
            print(f"  ✓ {directory}: {len(files)} fichiers")
            for file in files[:3]:  # Afficher les 3 premiers
                file_path = os.path.join(directory, file)
                size = os.path.getsize(file_path)
                print(f"    - {file} ({size} bytes)")
        else:
            print(f"  ⚠️  Dossier {directory} n'existe pas")

def test_pages_accessibility():
    """Test l'accessibilité des pages"""
    print("\n=== Test des pages ===")
    
    pages = [
        '/',
        '/dashboard',
        '/stagiaires',
        '/stages',
        '/promotions',
        '/documents',
        '/chatbot'
    ]
    
    for page in pages:
        try:
            response = requests.get(f"{BASE_URL}{page}")
            if response.status_code == 200:
                print(f"  ✓ {page} - OK")
            else:
                print(f"  ⚠️  {page} - Status: {response.status_code}")
        except Exception as e:
            print(f"  ✗ {page} - Erreur: {e}")

def main():
    """Fonction principale de test"""
    print("🚀 Test de fonctionnalité StageManager")
    print("=" * 50)
    
    # Vérifier que le serveur est accessible
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print(f"❌ Serveur non accessible sur {BASE_URL}")
            return
        print(f"✅ Serveur accessible sur {BASE_URL}")
    except Exception as e:
        print(f"❌ Impossible de se connecter au serveur: {e}")
        return
    
    # Exécuter tous les tests
    test_pages_accessibility()
    test_basic_apis()
    test_crud_operations()
    test_document_generation()
    test_search_functionality()
    test_export_functionality()
    test_file_existence()
    
    print("\n" + "=" * 50)
    print("🎉 Tests terminés !")
    print("\n💡 Pour tester l'interface web, ouvrez: http://localhost:5000")

if __name__ == "__main__":
    main()
