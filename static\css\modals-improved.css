/* <PERSON>dales <PERSON> - Positionnement Adapté */

/* Overlay de la modale */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 1050;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

/* Container de la modale */
.modal-dialog {
    max-width: 90vw;
    max-height: 90vh;
    width: 100%;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Contenu de la modale */
.modal-content {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid #e5e7eb;
    max-height: 85vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

/* Header de la modale */
.modal-header {
    background: var(--primary-color);
    color: white;
    padding: 1.25rem 1.5rem;
    border-bottom: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: white;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Corps de la modale */
.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}

/* Footer de la modale */
.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    flex-shrink: 0;
}

/* Tailles spécifiques */
.modal-sm .modal-dialog {
    max-width: 400px;
}

.modal-lg .modal-dialog {
    max-width: 800px;
}

.modal-xl .modal-dialog {
    max-width: 1200px;
}

/* Modale plein écran sur mobile */
@media (max-width: 768px) {
    .modal {
        padding: 0;
    }
    
    .modal-dialog {
        max-width: 100vw;
        max-height: 100vh;
        margin: 0;
    }
    
    .modal-content {
        border-radius: 0;
        max-height: 100vh;
        height: 100vh;
    }
    
    .modal-header {
        padding: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-footer {
        padding: 1rem;
    }
}

/* Modales spécialisées */

/* Modale de confirmation */
.modal-confirm .modal-dialog {
    max-width: 450px;
}

.modal-confirm .modal-body {
    text-align: center;
    padding: 2rem 1.5rem;
}

.modal-confirm .confirm-icon {
    width: 4rem;
    height: 4rem;
    margin: 0 auto 1rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.modal-confirm.danger .confirm-icon {
    background: #fee2e2;
    color: #dc2626;
}

.modal-confirm.warning .confirm-icon {
    background: #fef3c7;
    color: #d97706;
}

.modal-confirm.info .confirm-icon {
    background: #dbeafe;
    color: #2563eb;
}

/* Modale de formulaire */
.modal-form .modal-dialog {
    max-width: 600px;
}

.modal-form .form-group {
    margin-bottom: 1.5rem;
}

.modal-form .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.modal-form .form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.modal-form .form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(45, 90, 39, 0.1);
}

/* Modale de détails */
.modal-details .modal-dialog {
    max-width: 700px;
}

.modal-details .detail-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.modal-details .detail-label {
    font-weight: 500;
    color: #6b7280;
}

.modal-details .detail-value {
    color: #111827;
}

/* Animations d'entrée */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes modalSlideOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
}

.modal-content.animate-in {
    animation: modalSlideIn 0.3s ease-out;
}

.modal-content.animate-out {
    animation: modalSlideOut 0.3s ease-in;
}

/* Boutons dans les modales */
.modal .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.modal .btn-primary {
    background: var(--primary-color);
    color: white;
}

.modal .btn-primary:hover {
    background: var(--primary-dark);
}

.modal .btn-secondary {
    background: #6b7280;
    color: white;
}

.modal .btn-secondary:hover {
    background: #4b5563;
}

.modal .btn-danger {
    background: #dc2626;
    color: white;
}

.modal .btn-danger:hover {
    background: #b91c1c;
}

/* Responsive pour tablettes */
@media (max-width: 1024px) and (min-width: 769px) {
    .modal-lg .modal-dialog,
    .modal-xl .modal-dialog {
        max-width: 90vw;
    }
}

/* Accessibilité */
.modal[aria-hidden="true"] {
    display: none;
}

.modal:focus {
    outline: none;
}

/* Scroll dans le body de la modale */
.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
