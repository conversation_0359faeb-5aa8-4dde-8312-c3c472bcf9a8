// Gestion des promotions
class PromotionsManager {
    constructor() {
        this.promotions = [];
        this.stagiaires = [];
        this.stages = [];
        this.filteredPromotions = [];
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.currentView = 'card';
        this.filters = {
            search: '',
            statut: '',
            annee: '',
            filiere: ''
        };
        
        this.init();
        this.bindEvents();
        this.loadData();
    }

    init() {
        // Éléments DOM
        this.tableView = document.getElementById('promotionTableView');
        this.cardView = document.getElementById('promotionCardView');
        this.tableViewBtn = document.getElementById('promotionTableViewBtn');
        this.cardViewBtn = document.getElementById('promotionCardViewBtn');
        this.promotionModal = document.getElementById('promotionModal');
        this.detailsModal = document.getElementById('detailsPromotionModal');
        this.promotionForm = document.getElementById('promotionForm');
        
        // Filtres
        this.searchInput = document.getElementById('searchPromotions');
        this.filterStatut = document.getElementById('filterStatutPromotion');
        this.filterAnnee = document.getElementById('filterAnnee');
        this.filterFiliere = document.getElementById('filterFiliere');
    }

    bindEvents() {
        // Boutons de vue
        this.tableViewBtn?.addEventListener('click', () => this.switchView('table'));
        this.cardViewBtn?.addEventListener('click', () => this.switchView('card'));
        
        // Modal nouvelle promotion
        document.getElementById('addPromotionBtn')?.addEventListener('click', () => this.openPromotionModal());
        document.getElementById('closePromotionModal')?.addEventListener('click', () => this.closePromotionModal());
        document.getElementById('cancelPromotion')?.addEventListener('click', () => this.closePromotionModal());
        document.getElementById('savePromotion')?.addEventListener('click', () => this.savePromotion());
        
        // Modal détails
        document.getElementById('closePromotionDetailsModal')?.addEventListener('click', () => this.closeDetailsModal());
        
        // Filtres
        this.searchInput?.addEventListener('input', (e) => {
            this.filters.search = e.target.value;
            this.debounce(() => this.applyFilters(), 300)();
        });
        
        this.filterStatut?.addEventListener('change', (e) => {
            this.filters.statut = e.target.value;
            this.applyFilters();
        });
        
        this.filterAnnee?.addEventListener('change', (e) => {
            this.filters.annee = e.target.value;
            this.applyFilters();
        });
        
        this.filterFiliere?.addEventListener('input', (e) => {
            this.filters.filiere = e.target.value;
            this.debounce(() => this.applyFilters(), 300)();
        });
        
        // Reset filtres
        document.getElementById('resetPromotionFilters')?.addEventListener('click', () => this.resetFilters());

        // Export
        document.getElementById('exportPromotionsBtn')?.addEventListener('click', () => this.showExportModal());
        
        // Pagination
        document.getElementById('promotionPrevPage')?.addEventListener('click', () => this.previousPage());
        document.getElementById('promotionNextPage')?.addEventListener('click', () => this.nextPage());
        
        // Fermer modals en cliquant à l'extérieur
        this.promotionModal?.addEventListener('click', (e) => {
            if (e.target === this.promotionModal) this.closePromotionModal();
        });
        
        this.detailsModal?.addEventListener('click', (e) => {
            if (e.target === this.detailsModal) this.closeDetailsModal();
        });
    }

    async loadData() {
        try {
            window.stageManager.showLoading();
            
            // Charger toutes les données nécessaires
            const [promotionsResponse, stagiairesResponse, stagesResponse] = await Promise.all([
                window.stageManager.get('/promotions'),
                window.stageManager.get('/stagiaires'),
                window.stageManager.get('/stages')
            ]);
            
            this.promotions = promotionsResponse;
            this.stagiaires = stagiairesResponse;
            this.stages = stagesResponse;
            
            this.populateFilters();
            this.updateStats();
            this.applyFilters();
            
        } catch (error) {
            console.error('Erreur lors du chargement des données:', error);
            window.stageManager.showToast('Erreur lors du chargement des données', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    populateFilters() {
        // Remplir le filtre des années
        const annees = [...new Set(this.promotions.map(p => p.annee))].sort((a, b) => b - a);
        const filterAnnee = document.getElementById('filterAnnee');
        if (filterAnnee) {
            filterAnnee.innerHTML = '<option value="">Toutes</option>' +
                annees.map(annee => `<option value="${annee}">${annee}</option>`).join('');
        }
    }

    updateStats() {
        const currentYear = new Date().getFullYear();
        const stats = {
            total: this.promotions.length,
            actives: this.promotions.filter(p => p.statut === 'active').length,
            totalStagiaires: this.stagiaires.length,
            anneeActuelle: this.promotions.filter(p => p.annee === currentYear).length
        };
        
        document.getElementById('totalPromotions').textContent = stats.total;
        document.getElementById('promotionsActives').textContent = stats.actives;
        document.getElementById('totalStagiairesPromotions').textContent = stats.totalStagiaires;
        document.getElementById('promotionsAnnee').textContent = stats.anneeActuelle;
    }

    applyFilters() {
        this.filteredPromotions = this.promotions.filter(promotion => {
            const matchSearch = !this.filters.search || 
                promotion.nom.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                promotion.filiere.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                promotion.annee.toString().includes(this.filters.search);
            
            const matchStatut = !this.filters.statut || promotion.statut === this.filters.statut;
            const matchAnnee = !this.filters.annee || promotion.annee.toString() === this.filters.annee;
            const matchFiliere = !this.filters.filiere || 
                promotion.filiere.toLowerCase().includes(this.filters.filiere.toLowerCase());
            
            return matchSearch && matchStatut && matchAnnee && matchFiliere;
        });
        
        this.currentPage = 1;
        this.renderPromotions();
        this.updatePagination();
    }

    renderPromotions() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageData = this.filteredPromotions.slice(startIndex, endIndex);
        
        if (this.currentView === 'table') {
            this.renderTableView(pageData);
        } else {
            this.renderCardView(pageData);
        }
    }

    renderTableView(promotions) {
        const tbody = document.getElementById('promotionsTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = promotions.map(promotion => {
            const stagiairesDansPromotion = this.getStagiairesByPromotion(promotion.id);
            
            return `
                <tr>
                    <td>
                        <input type="checkbox" value="${promotion.id}">
                    </td>
                    <td>
                        <div class="promotion-info">
                            <strong>${promotion.nom}</strong>
                        </div>
                    </td>
                    <td>${promotion.annee}</td>
                    <td>${promotion.filiere}</td>
                    <td>
                        ${promotion.date_debut ? window.formatDate(promotion.date_debut) : '-'} - 
                        ${promotion.date_fin ? window.formatDate(promotion.date_fin) : '-'}
                    </td>
                    <td>
                        <span class="effectif-info">${stagiairesDansPromotion.length}/${promotion.effectif_max}</span>
                    </td>
                    <td>${window.getStatusBadge(promotion.statut)}</td>
                    <td>${window.formatDate(promotion.date_creation)}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-secondary" onclick="promotionsManager.viewPromotion('${promotion.id}')" title="Voir détails">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="promotionsManager.editPromotion('${promotion.id}')" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="promotionsManager.deletePromotion('${promotion.id}')" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    renderCardView(promotions) {
        const grid = document.getElementById('promotionsGrid');
        if (!grid) return;
        
        grid.innerHTML = promotions.map(promotion => {
            const stagiairesDansPromotion = this.getStagiairesByPromotion(promotion.id);
            const stagesDansPromotion = this.getStagesByPromotion(promotion.id);
            const progressionEffectif = (stagiairesDansPromotion.length / promotion.effectif_max) * 100;
            
            return `
                <div class="promotion-card">
                    <div class="promotion-status-indicator ${promotion.statut}"></div>
                    
                    <div class="promotion-header">
                        <div class="promotion-info">
                            <h6>${promotion.nom}</h6>
                            <div class="promotion-meta">
                                <i class="fas fa-graduation-cap"></i>
                                <span>${promotion.filiere}</span>
                            </div>
                        </div>
                        <div class="promotion-year">${promotion.annee}</div>
                    </div>
                    
                    <div class="promotion-stats">
                        <div class="promotion-stat">
                            <div class="promotion-stat-number">${stagiairesDansPromotion.length}</div>
                            <div class="promotion-stat-label">Stagiaires</div>
                        </div>
                        <div class="promotion-stat">
                            <div class="promotion-stat-number">${stagesDansPromotion.length}</div>
                            <div class="promotion-stat-label">Stages</div>
                        </div>
                    </div>
                    
                    <div class="promotion-progress">
                        <div class="promotion-progress-bar">
                            <div class="promotion-progress-fill" style="width: ${progressionEffectif}%"></div>
                        </div>
                        <div class="promotion-progress-text">
                            <span>Effectif</span>
                            <span>${stagiairesDansPromotion.length}/${promotion.effectif_max}</span>
                        </div>
                    </div>
                    
                    ${promotion.date_debut && promotion.date_fin ? `
                    <div class="promotion-timeline">
                        <i class="fas fa-calendar-alt"></i>
                        <span>${window.formatDate(promotion.date_debut)} - ${window.formatDate(promotion.date_fin)}</span>
                    </div>
                    ` : ''}
                    
                    <div class="promotion-details">
                        <div class="promotion-detail">
                            <label>Statut:</label>
                            <span>${window.getStatusBadge(promotion.statut)}</span>
                        </div>
                        <div class="promotion-detail">
                            <label>Créée le:</label>
                            <span>${window.formatDate(promotion.date_creation)}</span>
                        </div>
                    </div>
                    
                    <div class="promotion-actions">
                        <button class="btn btn-sm btn-secondary" onclick="promotionsManager.viewPromotion('${promotion.id}')" title="Voir détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="promotionsManager.editPromotion('${promotion.id}')" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="promotionsManager.managePromotion('${promotion.id}')" title="Gérer">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="promotionsManager.deletePromotion('${promotion.id}')" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    getStagiairesByPromotion(promotionId) {
        return this.stages.filter(stage => stage.promotion_id === promotionId)
            .map(stage => this.stagiaires.find(s => s.id === stage.stagiaire_id))
            .filter(stagiaire => stagiaire);
    }

    getStagesByPromotion(promotionId) {
        return this.stages.filter(stage => stage.promotion_id === promotionId);
    }

    switchView(view) {
        this.currentView = view;
        
        if (view === 'table') {
            this.tableView.style.display = 'block';
            this.cardView.style.display = 'none';
            this.tableViewBtn.classList.add('active');
            this.cardViewBtn.classList.remove('active');
        } else {
            this.tableView.style.display = 'none';
            this.cardView.style.display = 'block';
            this.tableViewBtn.classList.remove('active');
            this.cardViewBtn.classList.add('active');
        }
        
        this.renderPromotions();
    }

    updatePagination() {
        const totalPages = Math.ceil(this.filteredPromotions.length / this.itemsPerPage);
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, this.filteredPromotions.length);
        
        // Mise à jour des informations
        const paginationInfo = document.getElementById('promotionPaginationInfo');
        if (paginationInfo) {
            paginationInfo.textContent = `Affichage de ${startItem} à ${endItem} sur ${this.filteredPromotions.length} promotions`;
        }
        
        // Mise à jour des boutons
        const prevBtn = document.getElementById('promotionPrevPage');
        const nextBtn = document.getElementById('promotionNextPage');
        
        if (prevBtn) prevBtn.disabled = this.currentPage === 1;
        if (nextBtn) nextBtn.disabled = this.currentPage === totalPages || totalPages === 0;
        
        // Génération des numéros de page
        this.generatePageNumbers(totalPages);
    }

    generatePageNumbers(totalPages) {
        const pagesContainer = document.getElementById('promotionPaginationPages');
        if (!pagesContainer) return;
        
        pagesContainer.innerHTML = '';
        
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.addEventListener('click', () => this.goToPage(i));
                pagesContainer.appendChild(pageBtn);
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.className = 'pagination-ellipsis';
                pagesContainer.appendChild(ellipsis);
            }
        }
    }

    goToPage(page) {
        this.currentPage = page;
        this.renderPromotions();
        this.updatePagination();
    }

    previousPage() {
        if (this.currentPage > 1) {
            this.goToPage(this.currentPage - 1);
        }
    }

    nextPage() {
        const totalPages = Math.ceil(this.filteredPromotions.length / this.itemsPerPage);
        if (this.currentPage < totalPages) {
            this.goToPage(this.currentPage + 1);
        }
    }

    resetFilters() {
        this.filters = {
            search: '',
            statut: '',
            annee: '',
            filiere: ''
        };
        
        // Reset des inputs
        if (this.searchInput) this.searchInput.value = '';
        if (this.filterStatut) this.filterStatut.value = '';
        if (this.filterAnnee) this.filterAnnee.value = '';
        if (this.filterFiliere) this.filterFiliere.value = '';
        
        this.applyFilters();
    }

    openPromotionModal(promotion = null) {
        const modal = this.promotionModal;
        const title = document.getElementById('promotionModalTitle');
        
        if (promotion) {
            title.textContent = 'Modifier la promotion';
            this.fillPromotionForm(promotion);
        } else {
            title.textContent = 'Nouvelle promotion';
            this.promotionForm.reset();
            document.getElementById('promotionId').value = '';
            // Définir l'année actuelle par défaut
            document.getElementById('anneePromotion').value = new Date().getFullYear();
        }
        
        modal.classList.add('show');
    }

    closePromotionModal() {
        this.promotionModal.classList.remove('show');
    }

    closeDetailsModal() {
        this.detailsModal.classList.remove('show');
    }

    fillPromotionForm(promotion) {
        document.getElementById('promotionId').value = promotion.id;
        document.getElementById('nomPromotion').value = promotion.nom;
        document.getElementById('anneePromotion').value = promotion.annee;
        document.getElementById('filierePromotion').value = promotion.filiere;
        document.getElementById('effectifMax').value = promotion.effectif_max;
        document.getElementById('dateDebutPromotion').value = promotion.date_debut || '';
        document.getElementById('dateFinPromotion').value = promotion.date_fin || '';
        document.getElementById('descriptionPromotion').value = promotion.description || '';
        document.getElementById('statutPromotion').value = promotion.statut;
    }

    async savePromotion() {
        const promotionId = document.getElementById('promotionId').value;
        
        const data = {
            nom: document.getElementById('nomPromotion').value,
            annee: parseInt(document.getElementById('anneePromotion').value),
            filiere: document.getElementById('filierePromotion').value,
            effectif_max: parseInt(document.getElementById('effectifMax').value),
            date_debut: document.getElementById('dateDebutPromotion').value,
            date_fin: document.getElementById('dateFinPromotion').value,
            description: document.getElementById('descriptionPromotion').value,
            statut: document.getElementById('statutPromotion').value
        };
        
        try {
            window.stageManager.showLoading();
            
            if (promotionId) {
                await window.stageManager.put(`/promotions/${promotionId}`, data);
                window.stageManager.showToast('Promotion modifiée avec succès', 'success');
            } else {
                await window.stageManager.post('/promotions', data);
                window.stageManager.showToast('Promotion créée avec succès', 'success');
            }
            
            this.closePromotionModal();
            await this.loadData();
            
        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            window.stageManager.showToast('Erreur lors de la sauvegarde', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    async editPromotion(id) {
        const promotion = this.promotions.find(p => p.id === id);
        if (promotion) {
            this.openPromotionModal(promotion);
        }
    }

    async viewPromotion(id) {
        const promotion = this.promotions.find(p => p.id === id);
        if (promotion) {
            this.showPromotionDetails(promotion);
        }
    }

    showPromotionDetails(promotion) {
        const stagiairesDansPromotion = this.getStagiairesByPromotion(promotion.id);
        const stagesDansPromotion = this.getStagesByPromotion(promotion.id);
        
        const detailsContainer = document.getElementById('promotionDetails');
        detailsContainer.innerHTML = `
            <div class="promotion-profile">
                <div class="profile-header">
                    <div class="profile-info">
                        <h3>${promotion.nom}</h3>
                        <p>${promotion.filiere} - ${promotion.annee}</p>
                        ${window.getStatusBadge(promotion.statut)}
                    </div>
                    <div class="profile-stats">
                        <div class="stat-item">
                            <span class="stat-number">${stagiairesDansPromotion.length}</span>
                            <span class="stat-label">Stagiaires</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${stagesDansPromotion.length}</span>
                            <span class="stat-label">Stages</span>
                        </div>
                    </div>
                </div>
                
                <div class="profile-details">
                    <div class="detail-section">
                        <h5>Informations générales</h5>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Nom:</label>
                                <span>${promotion.nom}</span>
                            </div>
                            <div class="detail-item">
                                <label>Année:</label>
                                <span>${promotion.annee}</span>
                            </div>
                            <div class="detail-item">
                                <label>Filière:</label>
                                <span>${promotion.filiere}</span>
                            </div>
                            <div class="detail-item">
                                <label>Effectif maximum:</label>
                                <span>${promotion.effectif_max}</span>
                            </div>
                            <div class="detail-item">
                                <label>Date de début:</label>
                                <span>${promotion.date_debut ? window.formatDate(promotion.date_debut) : 'Non définie'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Date de fin:</label>
                                <span>${promotion.date_fin ? window.formatDate(promotion.date_fin) : 'Non définie'}</span>
                            </div>
                        </div>
                    </div>
                    
                    ${promotion.description ? `
                    <div class="detail-section">
                        <h5>Description</h5>
                        <div class="promotion-description">${promotion.description}</div>
                    </div>
                    ` : ''}
                    
                    <div class="detail-section">
                        <h5>Stagiaires inscrits (${stagiairesDansPromotion.length})</h5>
                        <div class="stagiaires-list">
                            ${stagiairesDansPromotion.length > 0 ? 
                                stagiairesDansPromotion.map(stagiaire => `
                                    <div class="stagiaire-item">
                                        <div class="stagiaire-avatar">
                                            ${stagiaire.nom.charAt(0)}${stagiaire.prenom.charAt(0)}
                                        </div>
                                        <div class="stagiaire-info">
                                            <div class="stagiaire-name">${stagiaire.nom} ${stagiaire.prenom}</div>
                                            <div class="stagiaire-email">${stagiaire.email}</div>
                                        </div>
                                    </div>
                                `).join('') :
                                '<p class="text-muted">Aucun stagiaire inscrit dans cette promotion</p>'
                            }
                        </div>
                    </div>
                    
                    <div class="detail-section">
                        <h5>Informations système</h5>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Date de création:</label>
                                <span>${window.formatDateTime(promotion.date_creation)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Statut:</label>
                                <span>${promotion.statut}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.detailsModal.classList.add('show');
    }

    async deletePromotion(id) {
        const promotion = this.promotions.find(p => p.id === id);
        const stagiairesDansPromotion = this.getStagiairesByPromotion(id);
        
        if (stagiairesDansPromotion.length > 0) {
            window.stageManager.showToast('Impossible de supprimer une promotion contenant des stagiaires', 'error');
            return;
        }
        
        if (!confirm(`Êtes-vous sûr de vouloir supprimer la promotion "${promotion.nom}" ?`)) {
            return;
        }
        
        try {
            window.stageManager.showLoading();
            await window.stageManager.delete(`/promotions/${id}`);
            window.stageManager.showToast('Promotion supprimée avec succès', 'success');
            await this.loadData();
        } catch (error) {
            console.error('Erreur lors de la suppression:', error);
            window.stageManager.showToast('Erreur lors de la suppression', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    managePromotion(id) {
        // Fonctionnalité pour gérer la promotion (affectations, etc.)
        window.stageManager.showToast('Fonctionnalité de gestion en cours de développement', 'info');
    }

    showExportModal() {
        const modal = document.createElement('div');
        modal.className = 'modal show';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Exporter les promotions</h5>
                        <button class="modal-close" onclick="this.closest('.modal').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="form-label">Format d'export</label>
                            <select class="form-select" id="exportFormat">
                                <option value="excel">Excel (.xlsx)</option>
                                <option value="pdf">PDF</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Inclure les détails</label>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="includeStudents" checked>
                                <label class="form-check-label" for="includeStudents">
                                    Liste des stagiaires par promotion
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="includeStats" checked>
                                <label class="form-check-label" for="includeStats">
                                    Statistiques détaillées
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Filtrer par année</label>
                            <select class="form-select" id="exportAnnee">
                                <option value="">Toutes les années</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">Annuler</button>
                        <button class="btn btn-primary" onclick="promotionsManager.exportPromotions()">
                            <i class="fas fa-download"></i>
                            Exporter
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Remplir les années
        const anneeSelect = modal.querySelector('#exportAnnee');
        const annees = [...new Set(this.promotions.map(p => p.annee))].sort((a, b) => b - a);
        anneeSelect.innerHTML = '<option value="">Toutes les années</option>' +
            annees.map(annee => `<option value="${annee}">${annee}</option>`).join('');
    }

    async exportPromotions() {
        const modal = document.querySelector('.modal.show');
        const format = modal.querySelector('#exportFormat').value;
        const annee = modal.querySelector('#exportAnnee').value;
        const includeStudents = modal.querySelector('#includeStudents').checked;
        const includeStats = modal.querySelector('#includeStats').checked;

        try {
            window.stageManager.showLoading();

            // Filtrer les promotions
            let promotionsToExport = [...this.promotions];
            if (annee) {
                promotionsToExport = promotionsToExport.filter(p => p.annee.toString() === annee);
            }

            // Préparer les données d'export
            const exportData = promotionsToExport.map(promotion => {
                const stagiairesDansPromotion = this.getStagiairesByPromotion(promotion.id);
                const stagesDansPromotion = this.getStagesByPromotion(promotion.id);

                const data = {
                    'Nom': promotion.nom,
                    'Année': promotion.annee,
                    'Filière': promotion.filiere,
                    'Effectif Max': promotion.effectif_max,
                    'Effectif Actuel': stagiairesDansPromotion.length,
                    'Nombre de Stages': stagesDansPromotion.length,
                    'Statut': promotion.statut,
                    'Date de Début': promotion.date_debut || '',
                    'Date de Fin': promotion.date_fin || '',
                    'Date de Création': promotion.date_creation
                };

                if (includeStudents) {
                    data['Stagiaires'] = stagiairesDansPromotion.map(s => `${s.nom} ${s.prenom}`).join(', ');
                }

                return data;
            });

            // Générer le fichier selon le format
            if (format === 'excel') {
                this.generateExcelExport(exportData, includeStats);
            } else {
                this.generatePDFExport(exportData, includeStats);
            }

            window.stageManager.showToast('Export généré avec succès', 'success');
            modal.remove();

        } catch (error) {
            console.error('Erreur lors de l\'export:', error);
            window.stageManager.showToast('Erreur lors de l\'export', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    generateExcelExport(data, includeStats) {
        // Simulation de génération Excel
        console.log('Export Excel Promotions:', data);

        if (includeStats) {
            const stats = {
                'Total Promotions': data.length,
                'Promotions Actives': data.filter(p => p.Statut === 'active').length,
                'Total Stagiaires': data.reduce((sum, p) => sum + p['Effectif Actuel'], 0),
                'Moyenne Effectif': Math.round(data.reduce((sum, p) => sum + p['Effectif Actuel'], 0) / data.length)
            };
            console.log('Statistiques:', stats);
        }

        window.stageManager.showToast('Fonctionnalité Excel en cours de développement', 'info');
    }

    generatePDFExport(data, includeStats) {
        // Simulation de génération PDF
        console.log('Export PDF Promotions:', data);
        window.stageManager.showToast('Fonctionnalité PDF en cours de développement', 'info');
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialiser le gestionnaire des promotions
document.addEventListener('DOMContentLoaded', () => {
    window.promotionsManager = new PromotionsManager();
});
