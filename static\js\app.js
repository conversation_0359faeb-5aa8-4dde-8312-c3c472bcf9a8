// Application principale
class StageManager {
    constructor() {
        this.init();
        this.bindEvents();
        this.loadInitialData();
    }

    init() {
        // Configuration de l'application
        this.apiBase = '/api';
        this.currentPage = window.location.pathname;
        
        // Éléments DOM
        this.sidebar = document.getElementById('sidebar');
        this.mainContent = document.getElementById('mainContent');
        this.sidebarToggle = document.getElementById('sidebarToggle');
        this.mobileMenuToggle = document.getElementById('mobileMenuToggle');
        this.notificationBtn = document.getElementById('notificationBtn');
        this.notificationDropdown = document.getElementById('notificationDropdown');
        this.userMenuBtn = document.getElementById('userMenuBtn');
        this.userMenuDropdown = document.getElementById('userMenuDropdown');
        this.globalSearch = document.getElementById('globalSearch');
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.toastContainer = document.getElementById('toastContainer');
        
        // Marquer le lien actif dans la sidebar
        this.setActiveMenuItem();
    }

    bindEvents() {
        // Toggle sidebar
        if (this.sidebarToggle) {
            this.sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }
        
        if (this.mobileMenuToggle) {
            this.mobileMenuToggle.addEventListener('click', () => this.toggleMobileSidebar());
        }

        // Notifications dropdown
        if (this.notificationBtn) {
            this.notificationBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown(this.notificationDropdown);
            });
        }

        // User menu dropdown
        if (this.userMenuBtn) {
            this.userMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown(this.userMenuDropdown);
            });
        }

        // Fermer les dropdowns en cliquant ailleurs
        document.addEventListener('click', () => {
            this.closeAllDropdowns();
        });

        // Recherche globale
        if (this.globalSearch) {
            this.globalSearch.addEventListener('input', (e) => {
                this.debounce(() => this.performGlobalSearch(e.target.value), 300)();
            });
        }

        // Gestion du responsive
        window.addEventListener('resize', () => this.handleResize());
        this.handleResize();
    }

    toggleSidebar() {
        this.sidebar.classList.toggle('collapsed');
        this.mainContent.classList.toggle('expanded');
        localStorage.setItem('sidebarCollapsed', this.sidebar.classList.contains('collapsed'));
    }

    toggleMobileSidebar() {
        this.sidebar.classList.toggle('show');
    }

    toggleDropdown(dropdown) {
        if (!dropdown) return;
        
        // Fermer les autres dropdowns
        this.closeAllDropdowns();
        
        // Toggle le dropdown actuel
        dropdown.classList.toggle('show');
    }

    closeAllDropdowns() {
        const dropdowns = document.querySelectorAll('.notification-dropdown, .user-menu-dropdown');
        dropdowns.forEach(dropdown => dropdown.classList.remove('show'));
    }

    setActiveMenuItem() {
        const menuLinks = document.querySelectorAll('.menu-link');
        menuLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === this.currentPage) {
                link.classList.add('active');
            }
        });
    }

    handleResize() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            this.sidebar.classList.remove('collapsed');
            this.mainContent.classList.remove('expanded');
        } else {
            this.sidebar.classList.remove('show');
            // Restaurer l'état de la sidebar depuis localStorage
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                this.sidebar.classList.add('collapsed');
                this.mainContent.classList.add('expanded');
            }
        }
    }

    async loadInitialData() {
        try {
            this.showLoading();
            
            // Charger les statistiques pour le dashboard
            if (this.currentPage === '/dashboard' || this.currentPage === '/') {
                await this.loadDashboardStats();
            }
            
            // Charger les notifications
            await this.loadNotifications();
            
        } catch (error) {
            console.error('Erreur lors du chargement des données:', error);
            this.showToast('Erreur lors du chargement des données', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async loadDashboardStats() {
        try {
            const response = await fetch(`${this.apiBase}/stats`);
            const stats = await response.json();
            this.updateDashboardStats(stats);
        } catch (error) {
            console.error('Erreur lors du chargement des statistiques:', error);
        }
    }

    updateDashboardStats(stats) {
        // Mettre à jour les cartes de statistiques
        const statElements = {
            'total-stagiaires': stats.total_stagiaires,
            'total-stages': stats.total_stages,
            'total-promotions': stats.total_promotions,
            'stages-actifs': stats.stages_actifs
        };

        Object.entries(statElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value || 0;
            }
        });
    }

    async loadNotifications() {
        try {
            const response = await fetch(`${this.apiBase}/notifications`);
            const notifications = await response.json();
            this.updateNotificationBadge(notifications.length);
        } catch (error) {
            console.error('Erreur lors du chargement des notifications:', error);
        }
    }

    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'block' : 'none';
        }
    }

    async performGlobalSearch(query) {
        if (query.length < 2) return;

        try {
            const response = await fetch(`${this.apiBase}/search?q=${encodeURIComponent(query)}`);
            const results = await response.json();
            this.displaySearchResults(results);
        } catch (error) {
            console.error('Erreur lors de la recherche:', error);
        }
    }

    displaySearchResults(results) {
        // Afficher les résultats de recherche
        console.log('Résultats de recherche:', results);
        // TODO: Implémenter l'affichage des résultats
    }

    showLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.add('show');
        }
    }

    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.remove('show');
        }
    }

    showToast(message, type = 'info', duration = 5000) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <p>${message}</p>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        this.toastContainer.appendChild(toast);

        // Animer l'apparition
        setTimeout(() => toast.classList.add('show'), 100);

        // Supprimer automatiquement
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Méthodes utilitaires pour les API
    async apiRequest(endpoint, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(`${this.apiBase}${endpoint}`, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('Erreur API:', error);
            throw error;
        }
    }

    async get(endpoint) {
        return this.apiRequest(endpoint);
    }

    async post(endpoint, data) {
        return this.apiRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    async put(endpoint, data) {
        return this.apiRequest(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    async delete(endpoint) {
        return this.apiRequest(endpoint, {
            method: 'DELETE',
        });
    }
}

// Initialiser l'application quand le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    window.stageManager = new StageManager();
});

// Fonctions utilitaires globales
window.formatDate = function(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
};

window.formatDateTime = function(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('fr-FR');
};

window.getStatusBadge = function(status) {
    const statusMap = {
        'actif': 'badge-success',
        'inactif': 'badge-danger',
        'en_cours': 'badge-primary',
        'termine': 'badge-secondary',
        'suspendu': 'badge-warning'
    };
    
    const badgeClass = statusMap[status] || 'badge-secondary';
    return `<span class="badge ${badgeClass}">${status}</span>`;
};
