// Gestion des documents
class DocumentsManager {
    constructor() {
        this.documents = [];
        this.stagiaires = [];
        this.promotions = [];
        this.stages = [];
        this.filteredDocuments = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filters = {
            search: '',
            type: '',
            format: '',
            date: ''
        };
        
        this.init();
        this.bindEvents();
        this.loadData();
    }

    init() {
        // Éléments DOM
        this.generateDocModal = document.getElementById('generateDocModal');
        this.previewModal = document.getElementById('previewDocModal');
        this.generateDocForm = document.getElementById('generateDocForm');
        
        // Filtres
        this.searchInput = document.getElementById('searchDocuments');
        this.filterType = document.getElementById('filterTypeDoc');
        this.filterFormat = document.getElementById('filterFormat');
        this.filterDate = document.getElementById('filterDate');
    }

    bindEvents() {
        // Boutons de génération sur les cartes
        document.querySelectorAll('.generate-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const card = e.target.closest('.document-type-card');
                const type = card.getAttribute('data-type');
                this.openGenerateModal(type);
            });
        });
        
        // Modal génération
        document.getElementById('generateDocBtn')?.addEventListener('click', () => this.openGenerateModal());
        document.getElementById('closeGenerateDocModal')?.addEventListener('click', () => this.closeGenerateModal());
        document.getElementById('cancelGenerateDoc')?.addEventListener('click', () => this.closeGenerateModal());
        document.getElementById('generateDocument')?.addEventListener('click', () => this.generateDocument());
        
        // Modal aperçu
        document.getElementById('closePreviewModal')?.addEventListener('click', () => this.closePreviewModal());
        document.getElementById('closePreview')?.addEventListener('click', () => this.closePreviewModal());
        document.getElementById('downloadPreviewDoc')?.addEventListener('click', () => this.downloadDocument());
        
        // Filtres
        this.searchInput?.addEventListener('input', (e) => {
            this.filters.search = e.target.value;
            this.debounce(() => this.applyFilters(), 300)();
        });
        
        this.filterType?.addEventListener('change', (e) => {
            this.filters.type = e.target.value;
            this.applyFilters();
        });
        
        this.filterFormat?.addEventListener('change', (e) => {
            this.filters.format = e.target.value;
            this.applyFilters();
        });
        
        this.filterDate?.addEventListener('change', (e) => {
            this.filters.date = e.target.value;
            this.applyFilters();
        });
        
        // Reset filtres
        document.getElementById('resetDocFilters')?.addEventListener('click', () => this.resetFilters());
        
        // Actions en lot
        document.getElementById('bulkDownloadBtn')?.addEventListener('click', () => this.bulkDownload());
        document.getElementById('bulkDeleteBtn')?.addEventListener('click', () => this.bulkDelete());
        
        // Sélection multiple
        document.getElementById('selectAllDocs')?.addEventListener('change', (e) => {
            this.selectAllDocuments(e.target.checked);
        });
        
        // Changement de type de document
        document.getElementById('docType')?.addEventListener('change', (e) => {
            this.updateFormFields(e.target.value);
        });
        
        // Pagination
        document.getElementById('docPrevPage')?.addEventListener('click', () => this.previousPage());
        document.getElementById('docNextPage')?.addEventListener('click', () => this.nextPage());
        
        // Fermer modals en cliquant à l'extérieur
        this.generateDocModal?.addEventListener('click', (e) => {
            if (e.target === this.generateDocModal) this.closeGenerateModal();
        });
        
        this.previewModal?.addEventListener('click', (e) => {
            if (e.target === this.previewModal) this.closePreviewModal();
        });
    }

    async loadData() {
        try {
            window.stageManager.showLoading();
            
            // Charger toutes les données nécessaires
            const [documentsResponse, stagiairesResponse, promotionsResponse, stagesResponse] = await Promise.all([
                this.getDocuments(),
                window.stageManager.get('/stagiaires'),
                window.stageManager.get('/promotions'),
                window.stageManager.get('/stages')
            ]);
            
            this.documents = documentsResponse;
            this.stagiaires = stagiairesResponse;
            this.promotions = promotionsResponse;
            this.stages = stagesResponse;
            
            this.populateSelects();
            this.applyFilters();
            
        } catch (error) {
            console.error('Erreur lors du chargement des données:', error);
            window.stageManager.showToast('Erreur lors du chargement des données', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    async getDocuments() {
        return await window.stageManager.get('/documents');
    }

    populateSelects() {
        // Remplir le select des stagiaires
        const stagiaireSelect = document.getElementById('docStagiaire');
        if (stagiaireSelect) {
            stagiaireSelect.innerHTML = '<option value="">Sélectionner un stagiaire</option>' +
                this.stagiaires.map(s => `<option value="${s.id}">${s.nom} ${s.prenom}</option>`).join('');
        }
        
        // Remplir le select des promotions
        const promotionSelect = document.getElementById('docPromotion');
        if (promotionSelect) {
            promotionSelect.innerHTML = '<option value="">Sélectionner une promotion</option>' +
                this.promotions.map(p => `<option value="${p.id}">${p.nom}</option>`).join('');
        }
        
        // Remplir le select des stages
        const stageSelect = document.getElementById('docStage');
        if (stageSelect) {
            stageSelect.innerHTML = '<option value="">Sélectionner un stage</option>' +
                this.stages.map(s => {
                    const stagiaire = this.stagiaires.find(st => st.id === s.stagiaire_id);
                    const nom = stagiaire ? `${stagiaire.nom} ${stagiaire.prenom}` : 'Stagiaire inconnu';
                    return `<option value="${s.id}">${nom} - ${s.unite_affectation}</option>`;
                }).join('');
        }
    }

    updateFormFields(docType) {
        const stagiaireGroup = document.getElementById('stagiaireSelectGroup');
        const promotionGroup = document.getElementById('promotionSelectGroup');
        const stageGroup = document.getElementById('stageSelectGroup');
        
        // Masquer tous les groupes
        stagiaireGroup.style.display = 'none';
        promotionGroup.style.display = 'none';
        stageGroup.style.display = 'none';
        
        // Afficher les groupes appropriés selon le type
        switch (docType) {
            case 'convention':
            case 'attestation':
                stagiaireGroup.style.display = 'block';
                stageGroup.style.display = 'block';
                break;
            case 'rapport':
                stagiaireGroup.style.display = 'block';
                break;
            case 'liste':
                promotionGroup.style.display = 'block';
                break;
        }
        
        // Mettre à jour les formats disponibles
        this.updateFormatOptions(docType);
    }

    updateFormatOptions(docType) {
        const formatSelect = document.getElementById('docFormat');
        if (!formatSelect) return;
        
        let options = '<option value="">Sélectionner un format</option>';
        
        switch (docType) {
            case 'convention':
            case 'attestation':
            case 'rapport':
                options += '<option value="pdf">PDF</option>';
                options += '<option value="word">Word</option>';
                break;
            case 'liste':
                options += '<option value="pdf">PDF</option>';
                options += '<option value="excel">Excel</option>';
                break;
            default:
                options += '<option value="pdf">PDF</option>';
                options += '<option value="excel">Excel</option>';
                options += '<option value="word">Word</option>';
        }
        
        formatSelect.innerHTML = options;
    }

    applyFilters() {
        this.filteredDocuments = this.documents.filter(doc => {
            const stagiaire = this.stagiaires.find(s => s.id === doc.stagiaire_id);
            const promotion = this.promotions.find(p => p.id === doc.promotion_id);
            
            const searchText = [
                doc.nom,
                stagiaire ? `${stagiaire.nom} ${stagiaire.prenom}` : '',
                promotion ? promotion.nom : ''
            ].join(' ').toLowerCase();
            
            const matchSearch = !this.filters.search || 
                searchText.includes(this.filters.search.toLowerCase());
            
            const matchType = !this.filters.type || doc.type === this.filters.type;
            const matchFormat = !this.filters.format || doc.format === this.filters.format;
            
            let matchDate = true;
            if (this.filters.date) {
                const docDate = new Date(doc.date_creation).toISOString().split('T')[0];
                matchDate = docDate === this.filters.date;
            }
            
            return matchSearch && matchType && matchFormat && matchDate;
        });
        
        this.currentPage = 1;
        this.renderDocuments();
        this.updatePagination();
    }

    renderDocuments() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageData = this.filteredDocuments.slice(startIndex, endIndex);
        
        const tbody = document.getElementById('documentsTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = pageData.map(doc => {
            const stagiaire = this.stagiaires.find(s => s.id === doc.stagiaire_id);
            const promotion = this.promotions.find(p => p.id === doc.promotion_id);
            const entity = stagiaire ? `${stagiaire.nom} ${stagiaire.prenom}` : 
                         promotion ? promotion.nom : '-';
            
            return `
                <tr>
                    <td>
                        <input type="checkbox" value="${doc.id}" class="doc-checkbox">
                    </td>
                    <td>
                        <div class="document-info">
                            <strong>${doc.nom}</strong>
                        </div>
                    </td>
                    <td>
                        <span class="badge badge-primary">${this.getTypeLabel(doc.type)}</span>
                    </td>
                    <td>
                        <span class="format-badge format-${doc.format}">${doc.format.toUpperCase()}</span>
                    </td>
                    <td>${entity}</td>
                    <td>
                        <span class="file-size">${doc.taille}</span>
                    </td>
                    <td>${window.formatDateTime(doc.date_creation)}</td>
                    <td>
                        <span class="document-status ${doc.statut}">
                            <i class="fas fa-${this.getStatusIcon(doc.statut)}"></i>
                            ${this.getStatusLabel(doc.statut)}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-secondary" onclick="documentsManager.previewDocument('${doc.id}')" title="Aperçu">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="documentsManager.downloadDocument('${doc.id}')" title="Télécharger">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="documentsManager.deleteDocument('${doc.id}')" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    getTypeLabel(type) {
        const labels = {
            'convention': 'Convention',
            'attestation': 'Attestation',
            'rapport': 'Rapport',
            'liste': 'Liste'
        };
        return labels[type] || type;
    }

    getStatusIcon(status) {
        const icons = {
            'generated': 'check-circle',
            'processing': 'clock',
            'error': 'exclamation-circle'
        };
        return icons[status] || 'question-circle';
    }

    getStatusLabel(status) {
        const labels = {
            'generated': 'Généré',
            'processing': 'En cours',
            'error': 'Erreur'
        };
        return labels[status] || status;
    }

    updatePagination() {
        const totalPages = Math.ceil(this.filteredDocuments.length / this.itemsPerPage);
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, this.filteredDocuments.length);
        
        // Mise à jour des informations
        const paginationInfo = document.getElementById('docPaginationInfo');
        if (paginationInfo) {
            paginationInfo.textContent = `Affichage de ${startItem} à ${endItem} sur ${this.filteredDocuments.length} documents`;
        }
        
        // Mise à jour des boutons
        const prevBtn = document.getElementById('docPrevPage');
        const nextBtn = document.getElementById('docNextPage');
        
        if (prevBtn) prevBtn.disabled = this.currentPage === 1;
        if (nextBtn) nextBtn.disabled = this.currentPage === totalPages || totalPages === 0;
        
        // Génération des numéros de page
        this.generatePageNumbers(totalPages);
    }

    generatePageNumbers(totalPages) {
        const pagesContainer = document.getElementById('docPaginationPages');
        if (!pagesContainer) return;
        
        pagesContainer.innerHTML = '';
        
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.addEventListener('click', () => this.goToPage(i));
                pagesContainer.appendChild(pageBtn);
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.className = 'pagination-ellipsis';
                pagesContainer.appendChild(ellipsis);
            }
        }
    }

    goToPage(page) {
        this.currentPage = page;
        this.renderDocuments();
        this.updatePagination();
    }

    previousPage() {
        if (this.currentPage > 1) {
            this.goToPage(this.currentPage - 1);
        }
    }

    nextPage() {
        const totalPages = Math.ceil(this.filteredDocuments.length / this.itemsPerPage);
        if (this.currentPage < totalPages) {
            this.goToPage(this.currentPage + 1);
        }
    }

    resetFilters() {
        this.filters = {
            search: '',
            type: '',
            format: '',
            date: ''
        };
        
        // Reset des inputs
        if (this.searchInput) this.searchInput.value = '';
        if (this.filterType) this.filterType.value = '';
        if (this.filterFormat) this.filterFormat.value = '';
        if (this.filterDate) this.filterDate.value = '';
        
        this.applyFilters();
    }

    openGenerateModal(type = '') {
        const modal = this.generateDocModal;
        const title = document.getElementById('generateDocModalTitle');
        
        title.textContent = 'Générer un document';
        this.generateDocForm.reset();
        
        if (type) {
            document.getElementById('docType').value = type;
            this.updateFormFields(type);
        }
        
        modal.classList.add('show');
    }

    closeGenerateModal() {
        this.generateDocModal.classList.remove('show');
    }

    closePreviewModal() {
        this.previewModal.classList.remove('show');
    }

    async generateDocument() {
        const formData = {
            type: document.getElementById('docType').value,
            format: document.getElementById('docFormat').value,
            stagiaire_id: document.getElementById('docStagiaire').value,
            promotion_id: document.getElementById('docPromotion').value,
            stage_id: document.getElementById('docStage').value,
            fileName: document.getElementById('docFileName').value,
            options: {
                includeSignature: document.getElementById('includeSignature').checked,
                includeWatermark: document.getElementById('includeWatermark').checked,
                autoSend: document.getElementById('autoSend').checked
            }
        };

        // Validation
        if (!formData.type || !formData.format) {
            window.stageManager.showToast('Veuillez sélectionner un type et un format', 'warning');
            return;
        }

        // Validation selon le type
        if ((formData.type === 'convention' || formData.type === 'attestation') && !formData.stagiaire_id) {
            window.stageManager.showToast('Veuillez sélectionner un stagiaire', 'warning');
            return;
        }

        if (formData.type === 'liste' && !formData.promotion_id) {
            window.stageManager.showToast('Veuillez sélectionner une promotion', 'warning');
            return;
        }

        try {
            window.stageManager.showLoading();

            const response = await window.stageManager.post('/documents', formData);

            window.stageManager.showToast('Document généré avec succès', 'success');
            this.closeGenerateModal();
            await this.loadData();

        } catch (error) {
            console.error('Erreur lors de la génération:', error);
            window.stageManager.showToast('Erreur lors de la génération du document', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    previewDocument(id) {
        const doc = this.documents.find(d => d.id === id);
        if (!doc) return;
        
        const preview = document.getElementById('documentPreview');
        preview.innerHTML = `
            <div class="preview-placeholder">
                <i class="fas fa-file-${doc.format} fa-3x mb-3"></i>
                <h4>${doc.nom}</h4>
                <p>Aperçu du document ${this.getTypeLabel(doc.type)} au format ${doc.format.toUpperCase()}</p>
                <p class="text-muted">Fonctionnalité d'aperçu en cours de développement</p>
            </div>
        `;
        
        this.previewModal.classList.add('show');
    }

    downloadDocument(id) {
        const doc = this.documents.find(d => d.id === id);
        if (!doc) return;

        // Télécharger le document via l'API
        window.stageManager.showToast(`Téléchargement de ${doc.nom} en cours...`, 'info');

        // Créer un lien de téléchargement
        const link = document.createElement('a');
        link.href = `/api/documents/${id}/download`;
        link.download = doc.nom;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.stageManager.showToast('Document téléchargé avec succès', 'success');
    }

    async deleteDocument(id) {
        const doc = this.documents.find(d => d.id === id);
        if (!doc) return;

        if (!confirm(`Êtes-vous sûr de vouloir supprimer le document "${doc.nom}" ?`)) {
            return;
        }

        try {
            window.stageManager.showLoading();
            await window.stageManager.delete(`/documents/${id}`);
            window.stageManager.showToast('Document supprimé avec succès', 'success');
            await this.loadData();
        } catch (error) {
            console.error('Erreur lors de la suppression:', error);
            window.stageManager.showToast('Erreur lors de la suppression', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    selectAllDocuments(checked) {
        const checkboxes = document.querySelectorAll('.doc-checkbox');
        checkboxes.forEach(cb => cb.checked = checked);
    }

    bulkDownload() {
        const selected = this.getSelectedDocuments();
        if (selected.length === 0) {
            window.stageManager.showToast('Aucun document sélectionné', 'warning');
            return;
        }

        window.stageManager.showToast(`Téléchargement de ${selected.length} document(s) en cours...`, 'info');

        // Créer un lien de téléchargement pour l'archive ZIP
        const params = selected.map(id => `ids=${id}`).join('&');
        const link = document.createElement('a');
        link.href = `/api/bulk-download?${params}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
            window.stageManager.showToast('Archive téléchargée avec succès', 'success');
        }, 1000);
    }

    async bulkDelete() {
        const selected = this.getSelectedDocuments();
        if (selected.length === 0) {
            window.stageManager.showToast('Aucun document sélectionné', 'warning');
            return;
        }

        if (!confirm(`Êtes-vous sûr de vouloir supprimer ${selected.length} document(s) ?`)) {
            return;
        }

        try {
            window.stageManager.showLoading();

            // Supprimer chaque document individuellement
            for (const docId of selected) {
                await window.stageManager.delete(`/documents/${docId}`);
            }

            window.stageManager.showToast(`${selected.length} document(s) supprimé(s) avec succès`, 'success');
            await this.loadData();
        } catch (error) {
            console.error('Erreur lors de la suppression en lot:', error);
            window.stageManager.showToast('Erreur lors de la suppression', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    getSelectedDocuments() {
        const checkboxes = document.querySelectorAll('.doc-checkbox:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialiser le gestionnaire des documents
document.addEventListener('DOMContentLoaded', () => {
    window.documentsManager = new DocumentsManager();
});
