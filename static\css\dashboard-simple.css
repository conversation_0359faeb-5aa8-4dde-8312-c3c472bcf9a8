/* Dashboard Thème Simple */

/* Variables pour le dashboard simple */
:root {
    --dashboard-bg: #f8fafc;
    --dashboard-card-bg: #ffffff;
    --dashboard-border: #e2e8f0;
    --dashboard-text: #1e293b;
    --dashboard-text-muted: #64748b;
    --dashboard-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --dashboard-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Container principal du dashboard */
.dashboard-container {
    background-color: var(--dashboard-bg);
    min-height: 100vh;
    padding: 2rem;
}

/* Header du dashboard */
.dashboard-header {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--dashboard-shadow);
    border: 1px solid var(--dashboard-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-header h1 {
    color: var(--primary-color);
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dashboard-header p {
    color: var(--dashboard-text-muted);
    margin: 0.5rem 0 0 0;
    font-size: 1rem;
}

/* Cartes de statistiques simplifiées */
.dashboard-container .card {
    background: var(--dashboard-card-bg);
    border: 1px solid var(--dashboard-border);
    border-radius: 0.75rem;
    box-shadow: var(--dashboard-shadow);
    transition: all 0.2s ease;
    overflow: hidden;
}

.dashboard-container .card:hover {
    box-shadow: var(--dashboard-shadow-hover);
    transform: translateY(-2px);
}

.dashboard-container .card-body {
    padding: 1.5rem;
}

/* Icônes de statistiques simplifiées */
.dashboard-container .stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    box-shadow: var(--dashboard-shadow);
    animation: none; /* Supprime les animations militaires */
}

.dashboard-container .stat-icon::before {
    display: none; /* Supprime les effets de lueur */
}

/* Couleurs spécifiques pour les icônes */
.stat-icon-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-icon-success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-icon-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-icon-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

/* Texte des statistiques */
.dashboard-container .card h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dashboard-text);
    margin-bottom: 0.25rem;
    font-family: 'Inter', sans-serif;
}

.dashboard-container .card p {
    color: var(--dashboard-text-muted);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    margin: 0;
}

/* Tendances */
.stat-trend {
    margin-top: 0.75rem;
    font-size: 0.875rem;
}

.stat-trend .text-success {
    color: #10b981;
}

.stat-trend .text-muted {
    color: var(--dashboard-text-muted);
}

/* Graphiques - En-têtes verts conservés */
.dashboard-container .card-header {
    background: var(--primary-color) !important;
    color: white !important;
    border-bottom: none;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.dashboard-container .card-header h5 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: white;
}

.dashboard-container .card-header .card-actions select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.dashboard-container .card-header .card-actions select option {
    background: var(--primary-color);
    color: white;
}

/* Zone des graphiques */
.dashboard-container .card-body canvas {
    max-height: 300px;
}

/* Activités récentes */
.activity-item {
    padding: 1rem;
    border-bottom: 1px solid var(--dashboard-border);
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background-color: #f8fafc;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    color: var(--dashboard-text-muted);
    font-size: 0.875rem;
}

/* Alertes simplifiées */
.dashboard-container .alert {
    border: 1px solid var(--dashboard-border);
    border-radius: 0.5rem;
    background: white;
    box-shadow: var(--dashboard-shadow);
    animation: none;
}

.dashboard-container .alert::before {
    display: none;
}

/* Boutons d'action en ligne */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.action-btn-primary {
    background: #3b82f6;
    color: white;
}

.action-btn-primary:hover {
    background: #2563eb;
    color: white;
    text-decoration: none;
}

.action-btn-success {
    background: #10b981;
    color: white;
}

.action-btn-success:hover {
    background: #059669;
    color: white;
    text-decoration: none;
}

.action-btn-warning {
    background: #f59e0b;
    color: white;
}

.action-btn-warning:hover {
    background: #d97706;
    color: white;
    text-decoration: none;
}

.action-btn-secondary {
    background: #6b7280;
    color: white;
}

.action-btn-secondary:hover {
    background: #4b5563;
    color: white;
    text-decoration: none;
}

/* Responsive */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }
    
    .dashboard-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .dashboard-header h1 {
        font-size: 1.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .action-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Suppression des effets militaires pour le dashboard */
.dashboard-container .military-glow,
.dashboard-container .radar-scan,
.dashboard-container .military-pulse {
    animation: none !important;
    box-shadow: var(--dashboard-shadow) !important;
}

.dashboard-container .military-glow::before,
.dashboard-container .military-glow::after,
.dashboard-container .radar-scan::before {
    display: none !important;
}
