#!/usr/bin/env python3
"""
Script de test pour la génération de documents
"""

import requests
import json
import os
import sys

# Configuration
BASE_URL = 'http://localhost:5000'
API_URL = f'{BASE_URL}/api'

def test_document_generation():
    """Test la génération de tous les types de documents"""
    print("🧪 Test de génération de documents")
    print("=" * 50)
    
    # Test 1: Convention PDF
    print("\n📄 Test 1: Génération Convention PDF")
    convention_data = {
        "type": "convention",
        "format": "pdf",
        "stagiaire_id": "stg-001",
        "stage_id": "stage-001"
    }
    
    try:
        response = requests.post(f'{API_URL}/documents', json=convention_data)
        if response.status_code == 201:
            doc = response.json()
            print(f"  ✅ Convention générée: {doc['nom']}")
            print(f"     Taille: {doc['taille']}")
            print(f"     Chemin: {doc['chemin_fichier']}")
            
            # Vérifier que le fichier existe
            if os.path.exists(doc['chemin_fichier']):
                print(f"  ✅ Fichier créé avec succès")
            else:
                print(f"  ❌ Fichier non trouvé sur le disque")
        else:
            print(f"  ❌ Erreur: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"  ❌ Exception: {e}")
    
    # Test 2: Attestation PDF
    print("\n📜 Test 2: Génération Attestation PDF")
    attestation_data = {
        "type": "attestation",
        "format": "pdf",
        "stagiaire_id": "stg-002",
        "stage_id": "stage-002"
    }
    
    try:
        response = requests.post(f'{API_URL}/documents', json=attestation_data)
        if response.status_code == 201:
            doc = response.json()
            print(f"  ✅ Attestation générée: {doc['nom']}")
            print(f"     Taille: {doc['taille']}")
            print(f"     Chemin: {doc['chemin_fichier']}")
            
            if os.path.exists(doc['chemin_fichier']):
                print(f"  ✅ Fichier créé avec succès")
            else:
                print(f"  ❌ Fichier non trouvé sur le disque")
        else:
            print(f"  ❌ Erreur: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"  ❌ Exception: {e}")
    
    # Test 3: Liste Excel
    print("\n📊 Test 3: Génération Liste Excel")
    liste_excel_data = {
        "type": "liste",
        "format": "excel",
        "promotion_id": "promo-001"
    }
    
    try:
        response = requests.post(f'{API_URL}/documents', json=liste_excel_data)
        if response.status_code == 201:
            doc = response.json()
            print(f"  ✅ Liste Excel générée: {doc['nom']}")
            print(f"     Taille: {doc['taille']}")
            print(f"     Chemin: {doc['chemin_fichier']}")
            
            if os.path.exists(doc['chemin_fichier']):
                print(f"  ✅ Fichier créé avec succès")
            else:
                print(f"  ❌ Fichier non trouvé sur le disque")
        else:
            print(f"  ❌ Erreur: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"  ❌ Exception: {e}")
    
    # Test 4: Liste PDF
    print("\n📋 Test 4: Génération Liste PDF")
    liste_pdf_data = {
        "type": "liste",
        "format": "pdf",
        "promotion_id": "promo-001"
    }
    
    try:
        response = requests.post(f'{API_URL}/documents', json=liste_pdf_data)
        if response.status_code == 201:
            doc = response.json()
            print(f"  ✅ Liste PDF générée: {doc['nom']}")
            print(f"     Taille: {doc['taille']}")
            print(f"     Chemin: {doc['chemin_fichier']}")
            
            if os.path.exists(doc['chemin_fichier']):
                print(f"  ✅ Fichier créé avec succès")
            else:
                print(f"  ❌ Fichier non trouvé sur le disque")
        else:
            print(f"  ❌ Erreur: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"  ❌ Exception: {e}")

def test_document_download():
    """Test le téléchargement de documents"""
    print("\n📥 Test de téléchargement de documents")
    print("=" * 50)
    
    try:
        # Récupérer la liste des documents
        response = requests.get(f'{API_URL}/documents')
        if response.status_code == 200:
            documents = response.json()
            print(f"  📄 {len(documents)} document(s) disponible(s)")
            
            for doc in documents[:2]:  # Tester les 2 premiers
                print(f"\n  🔽 Test téléchargement: {doc['nom']}")
                
                download_response = requests.get(f"{API_URL}/documents/{doc['id']}/download")
                if download_response.status_code == 200:
                    print(f"    ✅ Téléchargement réussi ({len(download_response.content)} bytes)")
                else:
                    print(f"    ❌ Erreur téléchargement: {download_response.status_code}")
        else:
            print(f"  ❌ Erreur récupération documents: {response.status_code}")
    except Exception as e:
        print(f"  ❌ Exception: {e}")

def test_api_connectivity():
    """Test la connectivité avec l'API"""
    print("🔗 Test de connectivité API")
    print("=" * 50)
    
    try:
        response = requests.get(f'{API_URL}/stats')
        if response.status_code == 200:
            stats = response.json()
            print("  ✅ API accessible")
            print(f"     Stagiaires: {stats['total_stagiaires']}")
            print(f"     Stages: {stats['total_stages']}")
            print(f"     Promotions: {stats['total_promotions']}")
            return True
        else:
            print(f"  ❌ API non accessible: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Erreur connexion: {e}")
        return False

def check_dependencies():
    """Vérifier les dépendances nécessaires"""
    print("📦 Vérification des dépendances")
    print("=" * 50)
    
    dependencies = [
        'reportlab',
        'openpyxl',
        'pandas'
    ]
    
    missing = []
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"  ✅ {dep}")
        except ImportError:
            print(f"  ❌ {dep} - MANQUANT")
            missing.append(dep)
    
    if missing:
        print(f"\n⚠️  Dépendances manquantes: {', '.join(missing)}")
        print("   Installez avec: pip install " + " ".join(missing))
        return False
    
    return True

def main():
    """Fonction principale"""
    print("🧪 TEST COMPLET - GÉNÉRATION DE DOCUMENTS")
    print("=" * 60)
    
    # Vérifier les dépendances
    if not check_dependencies():
        print("\n❌ Tests interrompus - dépendances manquantes")
        sys.exit(1)
    
    # Tester la connectivité
    if not test_api_connectivity():
        print("\n❌ Tests interrompus - API non accessible")
        print("   Assurez-vous que l'application Flask est démarrée")
        sys.exit(1)
    
    # Tester la génération
    test_document_generation()
    
    # Tester le téléchargement
    test_document_download()
    
    print("\n" + "=" * 60)
    print("🎯 Tests terminés")
    
    # Vérifier les dossiers créés
    folders = ['uploads/conventions', 'uploads/attestations', 'uploads/listes']
    print("\n📁 Dossiers créés:")
    for folder in folders:
        if os.path.exists(folder):
            files = os.listdir(folder)
            print(f"  ✅ {folder} ({len(files)} fichier(s))")
        else:
            print(f"  ❌ {folder} - non créé")

if __name__ == '__main__':
    main()
