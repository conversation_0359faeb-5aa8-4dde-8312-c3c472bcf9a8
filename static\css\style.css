/* Variables CSS - Thème Militaire Moderne */
:root {
    /* Couleurs principales - Thème militaire marocain */
    --primary-color: #2d5a27;
    --primary-dark: #1a3d1a;
    --primary-light: #4a7c59;
    --secondary-color: #64748b;
    --accent-color: #e74c3c;        /* Rouge marocain */
    --accent-secondary: #f39c12;    /* Orange doré marocain */
    --accent-tertiary: #27ae60;     /* Vert marocain */

    /* Couleurs de statut */
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --success-color: #22c55e;
    --info-color: #3b82f6;
    --operational-color: #10b981;
    --maintenance-color: #f59e0b;
    --breakdown-color: #ef4444;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #1e293b;
    --bg-darker: #0f172a;
    --bg-military: linear-gradient(135deg, #2d5a27 0%, #1a3d1a 100%);
    --bg-military-light: linear-gradient(135deg, #4a7c59 0%, #2d5a27 100%);
    --bg-moroccan: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    --bg-moroccan-light: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    --bg-glass: rgba(255, 255, 255, 0.1);

    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-white: #ffffff;
    --text-light: #f8fafc;
    --text-military: #2d5a27;
    
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-military: #4a7c59;

    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-military: 0 8px 32px rgba(45, 90, 39, 0.3);
    --shadow-glow: 0 0 20px rgba(231, 76, 60, 0.4);
    --shadow-glow-secondary: 0 0 20px rgba(243, 156, 18, 0.4);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --topbar-height: 70px;
    
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Animations */
    --pulse-duration: 2s;
    --glow-duration: 1.5s;
    --scan-duration: 3s;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Arial Unicode MS', sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    direction: ltr; /* Support pour le texte arabe */
}

/* Support pour le texte arabe */
.arabic-text,
[lang="ar"],
.rtl {
    direction: rtl;
    text-align: right;
    font-family: 'Arial Unicode MS', 'Tahoma', sans-serif;
}

.arabic-name {
    font-family: 'Arial Unicode MS', 'Tahoma', sans-serif;
    font-weight: 600;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--bg-military);
    color: var(--text-white);
    transition: all var(--transition-normal);
    z-index: 1000;
    overflow-y: auto;
    box-shadow: var(--shadow-military);
    transform: translateX(0);
    display: flex;
    visibility: visible;
    border-right: 3px solid var(--accent-color);
    backdrop-filter: blur(10px);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(231, 76, 60, 0.1) 50%, transparent 70%);
    animation: scan var(--scan-duration) infinite;
    pointer-events: none;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-white);
}

.logo i {
    font-size: 1.5rem;
    color: var(--primary-light);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-white);
    font-size: 1.125rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
    list-style: none;
    padding: 1rem 0;
}

.menu-item {
    margin: 0.25rem 0;
}

.menu-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all var(--transition-normal);
    border-radius: var(--radius-military);
    position: relative;
    margin: 0.25rem 0.5rem;
    overflow: hidden;
}

.menu-link::before {
    content: '';
    position: absolute;
    left: -100%;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.3), transparent);
    transition: left var(--transition-normal);
}

.menu-link:hover {
    background: rgba(231, 76, 60, 0.2);
    color: var(--text-white);
    transform: translateX(5px);
    box-shadow: var(--shadow-glow);
}

.menu-link:hover::before {
    left: 100%;
}

.menu-link.active {
    background: linear-gradient(90deg, var(--accent-color), var(--accent-secondary));
    color: var(--text-white);
    box-shadow: var(--shadow-military);
    animation: pulse-military var(--pulse-duration) infinite;
}

.menu-link.active::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, var(--accent-secondary), var(--accent-color));
    box-shadow: 0 0 10px var(--accent-color);
}

.menu-link i {
    font-size: 1.125rem;
    width: 1.25rem;
    text-align: center;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.25rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    font-size: 0.875rem;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    padding: 2rem;
    background-color: var(--bg-secondary);
    transition: all var(--transition-normal);
}

.main-content.expanded {
    margin-left: var(--sidebar-collapsed-width);
}

/* Topbar */
.topbar {
    background-color: var(--bg-primary);
    height: var(--topbar-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
}

.mobile-menu-toggle:hover {
    background-color: var(--bg-tertiary);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Search Box */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box input {
    width: 300px;
    padding: 0.625rem 1rem 0.625rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background-color: var(--bg-secondary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-box i {
    position: absolute;
    left: 0.875rem;
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Notifications */
.notifications {
    position: relative;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 1.125rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.notification-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.notification-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background-color: var(--danger-color);
    color: var(--text-white);
    font-size: 0.625rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 0.75rem;
    min-width: 1.125rem;
    text-align: center;
}

.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: 1000;
    margin-top: 0.5rem;
}

.notification-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
    font-size: 1rem;
    font-weight: 600;
}

.mark-all-read {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.75rem;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
}

.mark-all-read:hover {
    background-color: var(--bg-tertiary);
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    border-bottom: 1px solid var(--border-light);
    transition: all var(--transition-fast);
}

.notification-item:hover {
    background-color: var(--bg-secondary);
}

.notification-item.unread {
    background-color: rgba(79, 70, 229, 0.05);
}

.notification-item i {
    margin-top: 0.125rem;
    font-size: 0.875rem;
}

.notification-content {
    flex: 1;
}

.notification-content p {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* User Menu */
.user-menu {
    position: relative;
}

.user-menu-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.user-menu-btn:hover {
    background-color: var(--bg-tertiary);
}

.user-menu-btn .user-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
}

.user-menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: 1000;
    margin-top: 0.5rem;
    padding: 0.5rem 0;
}

.user-menu-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-menu-dropdown .menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.625rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.user-menu-dropdown .menu-item:hover {
    background-color: var(--bg-secondary);
}

.user-menu-dropdown hr {
    border: none;
    border-top: 1px solid var(--border-color);
    margin: 0.5rem 0;
}

/* Page Content */
.page-content {
    padding: 2rem;
    min-height: calc(100vh - var(--topbar-height));
}

/* Utility Classes */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
    color: var(--text-white);
}

.spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--text-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animations militaires */
@keyframes scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes pulse-military {
    0%, 100% {
        box-shadow: 0 0 5px var(--accent-color);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 20px var(--accent-color), 0 0 30px var(--accent-color);
        transform: scale(1.02);
    }
}

@keyframes glow {
    0%, 100% {
        text-shadow: 0 0 5px var(--accent-color);
    }
    50% {
        text-shadow: 0 0 20px var(--accent-color), 0 0 30px var(--accent-color);
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    0% {
        transform: translateY(30px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 1rem;
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    transition: all var(--transition-normal);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--danger-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.info {
    border-left: 4px solid var(--info-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
        z-index: 1001; /* Assurer que la sidebar est au-dessus */
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-menu-toggle {
        display: block;
    }
}

/* Assurer que la sidebar est visible sur desktop */
@media (min-width: 769px) {
    .sidebar {
        transform: translateX(0) !important;
        display: block !important;
        visibility: visible !important;
    }

    .main-content {
        margin-left: var(--sidebar-width);
    }

    .main-content.expanded {
        margin-left: var(--sidebar-collapsed-width);
    }

    .mobile-menu-toggle {
        display: none;
    }
}
    
    .search-box input {
        width: 200px;
    }
    
    .page-content {
        padding: 1rem;
    }
    
    .topbar {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .search-box {
        display: none;
    }

    .notification-dropdown,
    .user-menu-dropdown {
        width: 280px;
        right: -1rem;
    }
}

/* Cards */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--border-color);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--text-white);
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--text-white);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--text-white);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.625rem 0.875rem;
    font-size: 0.875rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-control:disabled {
    background-color: var(--bg-tertiary);
    opacity: 0.6;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* Tables */
.table-container {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.875rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: var(--bg-secondary);
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
}

.table tbody tr:hover {
    background-color: var(--bg-secondary);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.625rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: var(--radius-sm);
    white-space: nowrap;
}

.badge-primary {
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
}

.badge-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.badge-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.badge-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* Grid System */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col {
    flex: 1;
    padding: 0 0.75rem;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

@media (max-width: 768px) {
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Styles spécifiques pour les pages */
.page-header {
    margin-bottom: 2rem;
}

.page-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-header-left h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
}

.page-description {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.page-header-right {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Styles pour les formats de fichiers */
.format-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.format-pdf {
    background-color: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.format-excel {
    background-color: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.format-word {
    background-color: rgba(59, 130, 246, 0.1);
    color: #2563eb;
}

/* Styles pour les avatars */
.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    font-weight: 600;
    border-radius: inherit;
}

/* Styles pour les détails */
.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.detail-item span {
    color: var(--text-primary);
}

.detail-section {
    margin-bottom: 2rem;
}

.detail-section h5 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

/* Styles pour les profils */
.profile-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border-radius: var(--radius-lg);
}

.profile-info h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.profile-info p {
    margin: 0 0 1rem 0;
    opacity: 0.9;
}

.profile-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.75rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Styles pour les actions en lot */
.bulk-actions {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

/* Styles pour les indicateurs de statut */
.status-indicator {
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background-color: var(--success-color);
}

.status-indicator.offline {
    background-color: var(--text-muted);
}

/* Styles pour les tooltips */
[title] {
    position: relative;
}

/* Styles pour les codes */
code {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    padding: 0.125rem 0.375rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

/* Styles pour les listes */
.list-unstyled {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* Styles pour les séparateurs */
hr {
    border: none;
    border-top: 1px solid var(--border-color);
    margin: 1rem 0;
}

/* Styles pour les alertes */
.alert {
    padding: 1rem;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: var(--danger-color);
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
    color: var(--info-color);
}

/* Styles pour les résultats de recherche */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-result-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.search-result-item:hover {
    background-color: var(--bg-secondary);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    flex-shrink: 0;
}

.search-result-content {
    flex: 1;
}

.search-result-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.search-result-subtitle {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.search-result-type {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    background-color: var(--bg-tertiary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
}

.search-no-results {
    padding: 1rem;
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
}

/* Position relative pour le conteneur de recherche */
.search-container {
    position: relative;
}
