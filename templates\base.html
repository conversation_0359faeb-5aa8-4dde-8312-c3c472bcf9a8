<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestion des Stages{% endblock %}</title>
    
    <!-- CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar" style="transform: translateX(0); display: flex; visibility: visible;">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-graduation-cap"></i>
                <span>StageManager</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <ul class="sidebar-menu">
            <li class="menu-item">
                <a href="{{ url_for('dashboard') }}" class="menu-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Tableau de bord</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="{{ url_for('stagiaires') }}" class="menu-link">
                    <i class="fas fa-users"></i>
                    <span>Stagiaires</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="{{ url_for('stages') }}" class="menu-link">
                    <i class="fas fa-briefcase"></i>
                    <span>Stages</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="{{ url_for('promotions') }}" class="menu-link">
                    <i class="fas fa-layer-group"></i>
                    <span>Promotions</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="{{ url_for('documents') }}" class="menu-link">
                    <i class="fas fa-file-alt"></i>
                    <span>Documents</span>
                </a>
            </li>
            <li class="menu-item">
                <a href="{{ url_for('chatbot') }}" class="menu-link">
                    <i class="fas fa-robot"></i>
                    <span>Assistant IA</span>
                </a>
            </li>
        </ul>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name">Admin</span>
                    <span class="user-role">Administrateur</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content" id="mainContent" style="margin-left: 280px;">
        <!-- Top Bar -->
        <header class="topbar">
            <div class="topbar-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">{% block page_title %}Tableau de bord{% endblock %}</h1>
            </div>
            
            <div class="topbar-right">
                <div class="search-box search-container">
                    <input type="text" placeholder="Rechercher..." id="globalSearch">
                    <i class="fas fa-search"></i>
                </div>
                
                <div class="notifications">
                    <button class="notification-btn" id="notificationBtn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <div class="notification-dropdown" id="notificationDropdown">
                        <div class="notification-header">
                            <h3>Notifications</h3>
                            <button class="mark-all-read">Tout marquer comme lu</button>
                        </div>
                        <div class="notification-list">
                            <div class="notification-item unread">
                                <i class="fas fa-exclamation-circle text-warning"></i>
                                <div class="notification-content">
                                    <p>Stage de Jean Dupont expire dans 3 jours</p>
                                    <span class="notification-time">Il y a 2h</span>
                                </div>
                            </div>
                            <div class="notification-item unread">
                                <i class="fas fa-user-plus text-success"></i>
                                <div class="notification-content">
                                    <p>Nouveau stagiaire inscrit</p>
                                    <span class="notification-time">Il y a 5h</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <i class="fas fa-file-pdf text-info"></i>
                                <div class="notification-content">
                                    <p>Document généré avec succès</p>
                                    <span class="notification-time">Hier</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="user-menu">
                    <button class="user-menu-btn" id="userMenuBtn">
                        <img src="https://via.placeholder.com/32x32/4f46e5/ffffff?text=A" alt="Avatar" class="user-avatar">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-menu-dropdown" id="userMenuDropdown">
                        <a href="#" class="menu-item">
                            <i class="fas fa-user"></i>
                            Profil
                        </a>
                        <a href="#" class="menu-item">
                            <i class="fas fa-cog"></i>
                            Paramètres
                        </a>
                        <hr>
                        <a href="#" class="menu-item">
                            <i class="fas fa-sign-out-alt"></i>
                            Déconnexion
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <div class="page-content">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Chargement...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>

    <!-- Script pour forcer l'affichage de la sidebar -->
    <script>
        // Fonction exécutée immédiatement pour forcer l'affichage de la sidebar
        (function() {
            function forceSidebarDisplay() {
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.getElementById('mainContent');
                const isMobile = window.innerWidth <= 768;

                if (sidebar && !isMobile) {
                    // Forcer l'affichage sur desktop
                    sidebar.style.transform = 'translateX(0)';
                    sidebar.style.display = 'flex';
                    sidebar.style.visibility = 'visible';
                    sidebar.style.position = 'fixed';
                    sidebar.style.left = '0';
                    sidebar.style.top = '0';
                    sidebar.style.width = '280px';
                    sidebar.style.height = '100vh';
                    sidebar.style.zIndex = '1000';

                    // Assurer la marge du contenu principal
                    if (mainContent) {
                        mainContent.style.marginLeft = '280px';
                    }

                    console.log('✅ Sidebar forcée à être visible');
                }
            }

            // Exécuter immédiatement
            forceSidebarDisplay();

            // Exécuter après le chargement du DOM
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', forceSidebarDisplay);
            }

            // Exécuter après le chargement complet
            window.addEventListener('load', forceSidebarDisplay);

            // Exécuter après redimensionnement
            window.addEventListener('resize', function() {
                setTimeout(forceSidebarDisplay, 100);
            });
        })();
    </script>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
