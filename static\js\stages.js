// Gestion des stages
class StagesManager {
    constructor() {
        this.stages = [];
        this.stagiaires = [];
        this.typesStages = [];
        this.promotions = [];
        this.filteredStages = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.currentView = 'table';
        this.filters = {
            search: '',
            statut: '',
            type: '',
            periode: '',
            unite: ''
        };
        
        this.init();
        this.bindEvents();
        this.loadData();
    }

    init() {
        // Éléments DOM
        this.tableView = document.getElementById('stageTableView');
        this.cardView = document.getElementById('stageCardView');
        this.timelineView = document.getElementById('stageTimelineView');
        this.tableViewBtn = document.getElementById('stageTableViewBtn');
        this.cardViewBtn = document.getElementById('stageCardViewBtn');
        this.timelineViewBtn = document.getElementById('stageTimelineViewBtn');
        this.stageModal = document.getElementById('stageModal');
        this.detailsModal = document.getElementById('detailsStageModal');
        this.stageForm = document.getElementById('stageForm');
        
        // Filtres
        this.searchInput = document.getElementById('searchStages');
        this.filterStatut = document.getElementById('filterStatutStage');
        this.filterType = document.getElementById('filterTypeStage');
        this.filterPeriode = document.getElementById('filterPeriode');
        this.filterUnite = document.getElementById('filterUnite');
    }

    bindEvents() {
        // Boutons de vue
        this.tableViewBtn?.addEventListener('click', () => this.switchView('table'));
        this.cardViewBtn?.addEventListener('click', () => this.switchView('card'));
        this.timelineViewBtn?.addEventListener('click', () => this.switchView('timeline'));
        
        // Modal nouveau stage
        document.getElementById('addStageBtn')?.addEventListener('click', () => this.openStageModal());
        document.getElementById('closeStageModal')?.addEventListener('click', () => this.closeStageModal());
        document.getElementById('cancelStage')?.addEventListener('click', () => this.closeStageModal());
        document.getElementById('saveStage')?.addEventListener('click', () => this.saveStage());
        
        // Modal détails
        document.getElementById('closeStageDetailsModal')?.addEventListener('click', () => this.closeDetailsModal());
        
        // Filtres
        this.searchInput?.addEventListener('input', (e) => {
            this.filters.search = e.target.value;
            this.debounce(() => this.applyFilters(), 300)();
        });
        
        this.filterStatut?.addEventListener('change', (e) => {
            this.filters.statut = e.target.value;
            this.applyFilters();
        });
        
        this.filterType?.addEventListener('change', (e) => {
            this.filters.type = e.target.value;
            this.applyFilters();
        });
        
        this.filterPeriode?.addEventListener('change', (e) => {
            this.filters.periode = e.target.value;
            this.applyFilters();
        });
        
        this.filterUnite?.addEventListener('input', (e) => {
            this.filters.unite = e.target.value;
            this.debounce(() => this.applyFilters(), 300)();
        });
        
        // Reset filtres
        document.getElementById('resetStageFilters')?.addEventListener('click', () => this.resetFilters());

        // Export
        document.getElementById('exportStagesBtn')?.addEventListener('click', () => this.showExportModal());
        
        // Pagination
        document.getElementById('stagePrevPage')?.addEventListener('click', () => this.previousPage());
        document.getElementById('stageNextPage')?.addEventListener('click', () => this.nextPage());
        
        // Fermer modals en cliquant à l'extérieur
        this.stageModal?.addEventListener('click', (e) => {
            if (e.target === this.stageModal) this.closeStageModal();
        });
        
        this.detailsModal?.addEventListener('click', (e) => {
            if (e.target === this.detailsModal) this.closeDetailsModal();
        });
    }

    async loadData() {
        try {
            window.stageManager.showLoading();
            
            // Charger toutes les données nécessaires
            const [stagesResponse, stagiairesResponse, typesResponse, promotionsResponse] = await Promise.all([
                window.stageManager.get('/stages'),
                window.stageManager.get('/stagiaires'),
                window.stageManager.get('/types-stages'),
                window.stageManager.get('/promotions')
            ]);
            
            this.stages = stagesResponse;
            this.stagiaires = stagiairesResponse;
            this.typesStages = typesResponse;
            this.promotions = promotionsResponse;
            
            this.populateSelects();
            this.updateStats();
            this.applyFilters();
            
        } catch (error) {
            console.error('Erreur lors du chargement des données:', error);
            window.stageManager.showToast('Erreur lors du chargement des données', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    populateSelects() {
        // Remplir le select des stagiaires
        const stagiaireSelect = document.getElementById('stagiaireSelect');
        if (stagiaireSelect) {
            stagiaireSelect.innerHTML = '<option value="">Sélectionner un stagiaire</option>' +
                this.stagiaires.map(s => `<option value="${s.id}">${s.nom} ${s.prenom}</option>`).join('');
        }
        
        // Remplir le select des types de stages
        const typeSelect = document.getElementById('typeStageSelect');
        const filterTypeSelect = document.getElementById('filterTypeStage');
        const typeOptions = this.typesStages.map(t => `<option value="${t.id}">${t.libelle}</option>`).join('');
        
        if (typeSelect) {
            typeSelect.innerHTML = '<option value="">Sélectionner un type</option>' + typeOptions;
        }
        if (filterTypeSelect) {
            filterTypeSelect.innerHTML = '<option value="">Tous</option>' + typeOptions;
        }
        
        // Remplir le select des promotions
        const promotionSelect = document.getElementById('promotionSelect');
        if (promotionSelect) {
            promotionSelect.innerHTML = '<option value="">Sélectionner une promotion</option>' +
                this.promotions.map(p => `<option value="${p.id}">${p.nom}</option>`).join('');
        }
    }

    updateStats() {
        const stats = {
            total: this.stages.length,
            enCours: this.stages.filter(s => s.statut === 'en_cours').length,
            enAttente: this.stages.filter(s => s.statut === 'en_attente').length,
            termines: this.stages.filter(s => s.statut === 'termine').length
        };
        
        document.getElementById('totalStages').textContent = stats.total;
        document.getElementById('stagesEnCours').textContent = stats.enCours;
        document.getElementById('stagesEnAttente').textContent = stats.enAttente;
        document.getElementById('stagesTermines').textContent = stats.termines;
    }

    applyFilters() {
        this.filteredStages = this.stages.filter(stage => {
            const stagiaire = this.stagiaires.find(s => s.id === stage.stagiaire_id);
            const stagiaireNom = stagiaire ? `${stagiaire.nom} ${stagiaire.prenom}` : '';
            
            const matchSearch = !this.filters.search || 
                stagiaireNom.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                stage.unite_affectation.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                stage.responsable.toLowerCase().includes(this.filters.search.toLowerCase());
            
            const matchStatut = !this.filters.statut || stage.statut === this.filters.statut;
            const matchType = !this.filters.type || stage.type_stage_id == this.filters.type;
            const matchUnite = !this.filters.unite || 
                stage.unite_affectation.toLowerCase().includes(this.filters.unite.toLowerCase());
            
            let matchPeriode = true;
            if (this.filters.periode) {
                const now = new Date();
                const debut = new Date(stage.date_debut);
                const fin = new Date(stage.date_fin);
                
                switch (this.filters.periode) {
                    case 'current':
                        matchPeriode = debut <= now && fin >= now;
                        break;
                    case 'upcoming':
                        matchPeriode = debut > now;
                        break;
                    case 'past':
                        matchPeriode = fin < now;
                        break;
                }
            }
            
            return matchSearch && matchStatut && matchType && matchUnite && matchPeriode;
        });
        
        this.currentPage = 1;
        this.renderStages();
        this.updatePagination();
    }

    renderStages() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageData = this.filteredStages.slice(startIndex, endIndex);
        
        switch (this.currentView) {
            case 'table':
                this.renderTableView(pageData);
                break;
            case 'card':
                this.renderCardView(pageData);
                break;
            case 'timeline':
                this.renderTimelineView(this.filteredStages); // Timeline affiche tous les éléments
                break;
        }
    }

    renderTableView(stages) {
        const tbody = document.getElementById('stagesTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = stages.map(stage => {
            const stagiaire = this.stagiaires.find(s => s.id === stage.stagiaire_id);
            const typeStage = this.typesStages.find(t => t.id == stage.type_stage_id);
            const progression = this.calculateProgression(stage);
            
            return `
                <tr>
                    <td>
                        <input type="checkbox" value="${stage.id}">
                    </td>
                    <td>
                        <div class="user-info">
                            <strong>${stagiaire ? `${stagiaire.nom} ${stagiaire.prenom}` : 'N/A'}</strong>
                        </div>
                    </td>
                    <td>${typeStage ? typeStage.libelle : 'N/A'}</td>
                    <td>${stage.unite_affectation}</td>
                    <td>${stage.responsable}</td>
                    <td>${window.formatDate(stage.date_debut)}</td>
                    <td>${window.formatDate(stage.date_fin)}</td>
                    <td>${window.getStatusBadge(stage.statut)}</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progression}%"></div>
                        </div>
                        <div class="progress-text">${progression}%</div>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-secondary" onclick="stagesManager.viewStage('${stage.id}')" title="Voir détails">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="stagesManager.editStage('${stage.id}')" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="stagesManager.deleteStage('${stage.id}')" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    renderCardView(stages) {
        const grid = document.getElementById('stagesGrid');
        if (!grid) return;
        
        grid.innerHTML = stages.map(stage => {
            const stagiaire = this.stagiaires.find(s => s.id === stage.stagiaire_id);
            const typeStage = this.typesStages.find(t => t.id == stage.type_stage_id);
            const progression = this.calculateProgression(stage);
            
            return `
                <div class="stage-card">
                    <div class="stage-header">
                        <div class="stage-info">
                            <h6>${stagiaire ? `${stagiaire.nom} ${stagiaire.prenom}` : 'N/A'}</h6>
                            <div class="stage-meta">${typeStage ? typeStage.libelle : 'N/A'}</div>
                        </div>
                        <div class="stage-status">
                            ${window.getStatusBadge(stage.statut)}
                        </div>
                    </div>
                    
                    <div class="stage-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progression}%"></div>
                        </div>
                        <div class="progress-text">Progression: ${progression}%</div>
                    </div>
                    
                    <div class="stage-details">
                        <div class="stage-detail">
                            <label>Unité:</label>
                            <span>${stage.unite_affectation}</span>
                        </div>
                        <div class="stage-detail">
                            <label>Responsable:</label>
                            <span>${stage.responsable}</span>
                        </div>
                        <div class="stage-detail">
                            <label>Période:</label>
                            <span>${window.formatDate(stage.date_debut)} - ${window.formatDate(stage.date_fin)}</span>
                        </div>
                    </div>
                    
                    <div class="stage-actions">
                        <button class="btn btn-sm btn-secondary" onclick="stagesManager.viewStage('${stage.id}')" title="Voir détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="stagesManager.editStage('${stage.id}')" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="stagesManager.deleteStage('${stage.id}')" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    renderTimelineView(stages) {
        const timeline = document.getElementById('stageTimeline');
        if (!timeline) return;
        
        // Trier les stages par date de début
        const sortedStages = [...stages].sort((a, b) => new Date(a.date_debut) - new Date(b.date_debut));
        
        timeline.innerHTML = `
            <div class="timeline-line"></div>
            ${sortedStages.map(stage => {
                const stagiaire = this.stagiaires.find(s => s.id === stage.stagiaire_id);
                const typeStage = this.typesStages.find(t => t.id == stage.type_stage_id);
                
                return `
                    <div class="timeline-item">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <div class="timeline-date">${window.formatDate(stage.date_debut)} - ${window.formatDate(stage.date_fin)}</div>
                            <div class="timeline-title">${stagiaire ? `${stagiaire.nom} ${stagiaire.prenom}` : 'N/A'}</div>
                            <div class="timeline-description">
                                <strong>${typeStage ? typeStage.libelle : 'N/A'}</strong> - ${stage.unite_affectation}<br>
                                Responsable: ${stage.responsable}<br>
                                Statut: ${window.getStatusBadge(stage.statut)}
                            </div>
                        </div>
                    </div>
                `;
            }).join('')}
        `;
    }

    calculateProgression(stage) {
        const now = new Date();
        const debut = new Date(stage.date_debut);
        const fin = new Date(stage.date_fin);
        
        if (now < debut) return 0;
        if (now > fin) return 100;
        
        const total = fin - debut;
        const elapsed = now - debut;
        return Math.round((elapsed / total) * 100);
    }

    switchView(view) {
        this.currentView = view;
        
        // Masquer toutes les vues
        this.tableView.style.display = 'none';
        this.cardView.style.display = 'none';
        this.timelineView.style.display = 'none';
        
        // Retirer la classe active de tous les boutons
        this.tableViewBtn.classList.remove('active');
        this.cardViewBtn.classList.remove('active');
        this.timelineViewBtn.classList.remove('active');
        
        // Afficher la vue sélectionnée
        switch (view) {
            case 'table':
                this.tableView.style.display = 'block';
                this.tableViewBtn.classList.add('active');
                break;
            case 'card':
                this.cardView.style.display = 'block';
                this.cardViewBtn.classList.add('active');
                break;
            case 'timeline':
                this.timelineView.style.display = 'block';
                this.timelineViewBtn.classList.add('active');
                break;
        }
        
        this.renderStages();
    }

    updatePagination() {
        const totalPages = Math.ceil(this.filteredStages.length / this.itemsPerPage);
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, this.filteredStages.length);
        
        // Mise à jour des informations
        const paginationInfo = document.getElementById('stagePaginationInfo');
        if (paginationInfo) {
            paginationInfo.textContent = `Affichage de ${startItem} à ${endItem} sur ${this.filteredStages.length} stages`;
        }
        
        // Mise à jour des boutons
        const prevBtn = document.getElementById('stagePrevPage');
        const nextBtn = document.getElementById('stageNextPage');
        
        if (prevBtn) prevBtn.disabled = this.currentPage === 1;
        if (nextBtn) nextBtn.disabled = this.currentPage === totalPages || totalPages === 0;
        
        // Génération des numéros de page
        this.generatePageNumbers(totalPages);
    }

    generatePageNumbers(totalPages) {
        const pagesContainer = document.getElementById('stagePaginationPages');
        if (!pagesContainer) return;
        
        pagesContainer.innerHTML = '';
        
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.addEventListener('click', () => this.goToPage(i));
                pagesContainer.appendChild(pageBtn);
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.className = 'pagination-ellipsis';
                pagesContainer.appendChild(ellipsis);
            }
        }
    }

    goToPage(page) {
        this.currentPage = page;
        this.renderStages();
        this.updatePagination();
    }

    previousPage() {
        if (this.currentPage > 1) {
            this.goToPage(this.currentPage - 1);
        }
    }

    nextPage() {
        const totalPages = Math.ceil(this.filteredStages.length / this.itemsPerPage);
        if (this.currentPage < totalPages) {
            this.goToPage(this.currentPage + 1);
        }
    }

    resetFilters() {
        this.filters = {
            search: '',
            statut: '',
            type: '',
            periode: '',
            unite: ''
        };
        
        // Reset des inputs
        if (this.searchInput) this.searchInput.value = '';
        if (this.filterStatut) this.filterStatut.value = '';
        if (this.filterType) this.filterType.value = '';
        if (this.filterPeriode) this.filterPeriode.value = '';
        if (this.filterUnite) this.filterUnite.value = '';
        
        this.applyFilters();
    }

    openStageModal(stage = null) {
        const modal = this.stageModal;
        const title = document.getElementById('stageModalTitle');
        
        if (stage) {
            title.textContent = 'Modifier le stage';
            this.fillStageForm(stage);
        } else {
            title.textContent = 'Nouveau stage';
            this.stageForm.reset();
            document.getElementById('stageId').value = '';
        }
        
        modal.classList.add('show');
    }

    closeStageModal() {
        this.stageModal.classList.remove('show');
    }

    closeDetailsModal() {
        this.detailsModal.classList.remove('show');
    }

    fillStageForm(stage) {
        document.getElementById('stageId').value = stage.id;
        document.getElementById('stagiaireSelect').value = stage.stagiaire_id;
        document.getElementById('typeStageSelect').value = stage.type_stage_id;
        document.getElementById('dateDebut').value = stage.date_debut;
        document.getElementById('dateFin').value = stage.date_fin;
        document.getElementById('uniteAffectation').value = stage.unite_affectation;
        document.getElementById('responsableStage').value = stage.responsable;
        document.getElementById('objectifsStage').value = stage.objectifs || '';
        document.getElementById('statutStage').value = stage.statut;
        document.getElementById('promotionSelect').value = stage.promotion_id || '';
    }

    async saveStage() {
        const stageId = document.getElementById('stageId').value;
        
        const data = {
            stagiaire_id: document.getElementById('stagiaireSelect').value,
            type_stage_id: document.getElementById('typeStageSelect').value,
            date_debut: document.getElementById('dateDebut').value,
            date_fin: document.getElementById('dateFin').value,
            unite_affectation: document.getElementById('uniteAffectation').value,
            responsable: document.getElementById('responsableStage').value,
            objectifs: document.getElementById('objectifsStage').value,
            statut: document.getElementById('statutStage').value,
            promotion_id: document.getElementById('promotionSelect').value
        };
        
        try {
            window.stageManager.showLoading();
            
            if (stageId) {
                await window.stageManager.put(`/stages/${stageId}`, data);
                window.stageManager.showToast('Stage modifié avec succès', 'success');
            } else {
                await window.stageManager.post('/stages', data);
                window.stageManager.showToast('Stage créé avec succès', 'success');
            }
            
            this.closeStageModal();
            await this.loadData();
            
        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            window.stageManager.showToast('Erreur lors de la sauvegarde', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    async editStage(id) {
        const stage = this.stages.find(s => s.id === id);
        if (stage) {
            this.openStageModal(stage);
        }
    }

    async viewStage(id) {
        const stage = this.stages.find(s => s.id === id);
        if (stage) {
            this.showStageDetails(stage);
        }
    }

    showStageDetails(stage) {
        const stagiaire = this.stagiaires.find(s => s.id === stage.stagiaire_id);
        const typeStage = this.typesStages.find(t => t.id == stage.type_stage_id);
        const promotion = this.promotions.find(p => p.id === stage.promotion_id);
        const progression = this.calculateProgression(stage);
        
        const detailsContainer = document.getElementById('stageDetails');
        detailsContainer.innerHTML = `
            <div class="stage-profile">
                <div class="profile-header">
                    <div class="profile-info">
                        <h3>Stage de ${stagiaire ? `${stagiaire.nom} ${stagiaire.prenom}` : 'N/A'}</h3>
                        <p>${typeStage ? typeStage.libelle : 'N/A'}</p>
                        ${window.getStatusBadge(stage.statut)}
                    </div>
                    <div class="profile-progress">
                        <div class="progress-circle">
                            <span>${progression}%</span>
                        </div>
                    </div>
                </div>
                
                <div class="profile-details">
                    <div class="detail-section">
                        <h5>Informations du stage</h5>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Unité d'affectation:</label>
                                <span>${stage.unite_affectation}</span>
                            </div>
                            <div class="detail-item">
                                <label>Responsable:</label>
                                <span>${stage.responsable}</span>
                            </div>
                            <div class="detail-item">
                                <label>Date de début:</label>
                                <span>${window.formatDate(stage.date_debut)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Date de fin:</label>
                                <span>${window.formatDate(stage.date_fin)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Promotion:</label>
                                <span>${promotion ? promotion.nom : 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                    
                    ${stage.objectifs ? `
                    <div class="detail-section">
                        <h5>Objectifs du stage</h5>
                        <p>${stage.objectifs}</p>
                    </div>
                    ` : ''}
                    
                    <div class="detail-section">
                        <h5>Informations système</h5>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Date de création:</label>
                                <span>${window.formatDateTime(stage.date_creation)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Statut:</label>
                                <span>${stage.statut}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.detailsModal.classList.add('show');
    }

    async deleteStage(id) {
        if (!confirm('Êtes-vous sûr de vouloir supprimer ce stage ?')) {
            return;
        }
        
        try {
            window.stageManager.showLoading();
            await window.stageManager.delete(`/stages/${id}`);
            window.stageManager.showToast('Stage supprimé avec succès', 'success');
            await this.loadData();
        } catch (error) {
            console.error('Erreur lors de la suppression:', error);
            window.stageManager.showToast('Erreur lors de la suppression', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    showExportModal() {
        const modal = document.createElement('div');
        modal.className = 'modal show';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Exporter les stages</h5>
                        <button class="modal-close" onclick="this.closest('.modal').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="form-label">Format d'export</label>
                            <select class="form-select" id="exportFormat">
                                <option value="excel">Excel (.xlsx)</option>
                                <option value="pdf">PDF</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Filtrer par statut</label>
                            <select class="form-select" id="exportStatut">
                                <option value="">Tous les statuts</option>
                                <option value="en_cours">En cours</option>
                                <option value="en_attente">En attente</option>
                                <option value="termine">Terminé</option>
                                <option value="suspendu">Suspendu</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Période</label>
                            <select class="form-select" id="exportPeriode">
                                <option value="">Toutes les périodes</option>
                                <option value="current">Stages en cours</option>
                                <option value="upcoming">Stages à venir</option>
                                <option value="past">Stages passés</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">Annuler</button>
                        <button class="btn btn-primary" onclick="stagesManager.exportStages()">
                            <i class="fas fa-download"></i>
                            Exporter
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    async exportStages() {
        const modal = document.querySelector('.modal.show');
        const format = modal.querySelector('#exportFormat').value;
        const statut = modal.querySelector('#exportStatut').value;
        const periode = modal.querySelector('#exportPeriode').value;

        try {
            window.stageManager.showLoading();

            // Filtrer les données selon les critères
            let stagesToExport = [...this.stages];

            if (statut) {
                stagesToExport = stagesToExport.filter(s => s.statut === statut);
            }

            if (periode) {
                const now = new Date();
                stagesToExport = stagesToExport.filter(stage => {
                    const debut = new Date(stage.date_debut);
                    const fin = new Date(stage.date_fin);

                    switch (periode) {
                        case 'current':
                            return debut <= now && fin >= now;
                        case 'upcoming':
                            return debut > now;
                        case 'past':
                            return fin < now;
                        default:
                            return true;
                    }
                });
            }

            // Générer le fichier
            if (format === 'excel') {
                this.exportToExcel(stagesToExport);
            } else {
                this.exportToPDF(stagesToExport);
            }

            window.stageManager.showToast('Export généré avec succès', 'success');
            modal.remove();

        } catch (error) {
            console.error('Erreur lors de l\'export:', error);
            window.stageManager.showToast('Erreur lors de l\'export', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    exportToExcel(stages) {
        // Préparer les données pour Excel
        const data = stages.map(stage => {
            const stagiaire = this.stagiaires.find(s => s.id === stage.stagiaire_id);
            const typeStage = this.typesStages.find(t => t.id == stage.type_stage_id);

            return {
                'Stagiaire': stagiaire ? `${stagiaire.nom} ${stagiaire.prenom}` : 'N/A',
                'Type de stage': typeStage ? typeStage.libelle : 'N/A',
                'Unité d\'affectation': stage.unite_affectation,
                'Responsable': stage.responsable,
                'Date de début': stage.date_debut,
                'Date de fin': stage.date_fin,
                'Statut': stage.statut,
                'Objectifs': stage.objectifs || ''
            };
        });

        // Créer et télécharger le fichier Excel (simulation)
        console.log('Export Excel:', data);
        window.stageManager.showToast('Fonctionnalité Excel en cours de développement', 'info');
    }

    exportToPDF(stages) {
        // Générer un PDF (simulation)
        console.log('Export PDF:', stages);
        window.stageManager.showToast('Fonctionnalité PDF en cours de développement', 'info');
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialiser le gestionnaire des stages
document.addEventListener('DOMContentLoaded', () => {
    window.stagesManager = new StagesManager();
});
