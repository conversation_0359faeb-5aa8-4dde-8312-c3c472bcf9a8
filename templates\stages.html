{% extends "base.html" %}

{% block title %}Gestion des Stages - StageManager{% endblock %}
{% block page_title %}Gestion des Stages{% endblock %}

{% block content %}
<!-- Header avec actions -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-header-left">
            <h2 class="page-title">Stages</h2>
            <p class="page-description">Gérez les stages, affectations et suivis</p>
        </div>
        <div class="page-header-right">
            <button class="btn btn-secondary" id="exportStagesBtn">
                <i class="fas fa-download"></i>
                Exporter
            </button>
            <button class="btn btn-primary" id="addStageBtn">
                <i class="fas fa-plus"></i>
                Nouveau stage
            </button>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-3">
        <div class="stat-card">
            <div class="stat-icon bg-primary">
                <i class="fas fa-briefcase"></i>
            </div>
            <div class="stat-content">
                <h3 id="totalStages">0</h3>
                <p>Total stages</p>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="stat-card">
            <div class="stat-icon bg-success">
                <i class="fas fa-play-circle"></i>
            </div>
            <div class="stat-content">
                <h3 id="stagesEnCours">0</h3>
                <p>En cours</p>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="stat-card">
            <div class="stat-icon bg-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <h3 id="stagesEnAttente">0</h3>
                <p>En attente</p>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="stat-card">
            <div class="stat-icon bg-info">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <h3 id="stagesTermines">0</h3>
                <p>Terminés</p>
            </div>
        </div>
    </div>
</div>

<!-- Filtres et recherche -->
<div class="filters-section">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-3">
                    <div class="form-group">
                        <label class="form-label">Rechercher</label>
                        <div class="search-input">
                            <input type="text" class="form-control" id="searchStages" placeholder="Stagiaire, unité, responsable...">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">Statut</label>
                        <select class="form-select" id="filterStatutStage">
                            <option value="">Tous</option>
                            <option value="en_cours">En cours</option>
                            <option value="en_attente">En attente</option>
                            <option value="termine">Terminé</option>
                            <option value="suspendu">Suspendu</option>
                        </select>
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">Type</label>
                        <select class="form-select" id="filterTypeStage">
                            <option value="">Tous</option>
                        </select>
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">Période</label>
                        <select class="form-select" id="filterPeriode">
                            <option value="">Toutes</option>
                            <option value="current">En cours</option>
                            <option value="upcoming">À venir</option>
                            <option value="past">Passées</option>
                        </select>
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">Unité</label>
                        <input type="text" class="form-control" id="filterUnite" placeholder="Unité d'affectation">
                    </div>
                </div>
                <div class="col-1">
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-secondary w-100" id="resetStageFilters">
                            <i class="fas fa-undo"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Liste des stages -->
<div class="stages-section">
    <div class="card">
        <div class="card-header">
            <div class="card-header-content">
                <h5 class="card-title">Liste des stages</h5>
                <div class="card-actions">
                    <div class="view-toggle">
                        <button class="btn btn-sm btn-secondary active" id="stageTableViewBtn">
                            <i class="fas fa-table"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" id="stageCardViewBtn">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" id="stageTimelineViewBtn">
                            <i class="fas fa-calendar-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Vue tableau -->
            <div class="table-view" id="stageTableView">
                <div class="table-container">
                    <table class="table" id="stagesTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAllStages">
                                </th>
                                <th>Stagiaire</th>
                                <th>Type de stage</th>
                                <th>Unité d'affectation</th>
                                <th>Responsable</th>
                                <th>Date début</th>
                                <th>Date fin</th>
                                <th>Statut</th>
                                <th>Progression</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="stagesTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Vue cartes -->
            <div class="card-view" id="stageCardView" style="display: none;">
                <div class="stages-grid" id="stagesGrid">
                    <!-- Les cartes seront chargées dynamiquement -->
                </div>
            </div>
            
            <!-- Vue timeline -->
            <div class="timeline-view" id="stageTimelineView" style="display: none;">
                <div class="timeline-container" id="stageTimeline">
                    <!-- La timeline sera chargée dynamiquement -->
                </div>
            </div>
            
            <!-- Pagination -->
            <div class="pagination-section">
                <div class="pagination-info">
                    <span id="stagePaginationInfo">Affichage de 1 à 10 sur 0 stages</span>
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-sm btn-secondary" id="stagePrevPage" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="pagination-pages" id="stagePaginationPages">
                        <!-- Les numéros de page seront générés dynamiquement -->
                    </div>
                    <button class="btn btn-sm btn-secondary" id="stageNextPage" disabled>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Nouveau/Modifier Stage -->
<div class="modal" id="stageModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stageModalTitle">Nouveau stage</h5>
                <button class="modal-close" id="closeStageModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="stageForm">
                    <input type="hidden" id="stageId">
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Stagiaire *</label>
                                <select class="form-select" id="stagiaireSelect" required>
                                    <option value="">Sélectionner un stagiaire</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Type de stage *</label>
                                <select class="form-select" id="typeStageSelect" required>
                                    <option value="">Sélectionner un type</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Date de début *</label>
                                <input type="date" class="form-control" id="dateDebut" required>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Date de fin *</label>
                                <input type="date" class="form-control" id="dateFin" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Unité d'affectation *</label>
                                <input type="text" class="form-control" id="uniteAffectation" required>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Responsable *</label>
                                <input type="text" class="form-control" id="responsableStage" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Objectifs du stage</label>
                        <textarea class="form-control" id="objectifsStage" rows="3" placeholder="Décrivez les objectifs et missions du stage..."></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Statut</label>
                                <select class="form-select" id="statutStage">
                                    <option value="en_attente">En attente</option>
                                    <option value="en_cours">En cours</option>
                                    <option value="suspendu">Suspendu</option>
                                    <option value="termine">Terminé</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Promotion</label>
                                <select class="form-select" id="promotionSelect">
                                    <option value="">Sélectionner une promotion</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelStage">Annuler</button>
                <button class="btn btn-primary" id="saveStage">
                    <i class="fas fa-save"></i>
                    Enregistrer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Détails Stage -->
<div class="modal" id="detailsStageModal">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails du stage</h5>
                <button class="modal-close" id="closeStageDetailsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="stageDetails">
                    <!-- Le contenu sera chargé dynamiquement -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stat-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all var(--transition-fast);
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.stat-content p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.stages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.stage-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    transition: all var(--transition-fast);
}

.stage-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stage-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.stage-info h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.stage-meta {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.stage-progress {
    margin: 1rem 0;
}

.progress-bar {
    width: 100%;
    height: 0.5rem;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transition: width var(--transition-normal);
}

.progress-text {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

.stage-details {
    margin-bottom: 1rem;
}

.stage-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.stage-detail label {
    font-weight: 500;
    color: var(--text-secondary);
}

.stage-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.timeline-container {
    position: relative;
    padding: 2rem 0;
}

.timeline-line {
    position: absolute;
    left: 2rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 4rem;
}

.timeline-marker {
    position: absolute;
    left: 1.25rem;
    top: 0.5rem;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 3px solid var(--bg-primary);
    z-index: 1;
}

.timeline-content {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
}

.timeline-date {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.timeline-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.timeline-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

@media (max-width: 768px) {
    .stages-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
    
    .timeline-item {
        padding-left: 3rem;
    }
    
    .timeline-line {
        left: 1.5rem;
    }
    
    .timeline-marker {
        left: 0.75rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/stages.js') }}"></script>
{% endblock %}
