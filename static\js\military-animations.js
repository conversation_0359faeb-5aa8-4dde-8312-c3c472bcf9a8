/**
 * Animations Militaires Avancées
 * Gestion des effets visuels et animations pour le thème militaire
 */

class MilitaryAnimations {
    constructor() {
        this.particles = [];
        this.init();
    }

    init() {
        this.initScrollAnimations();
        this.initParticleSystem();
        this.initGlitchEffects();
        this.initHoverEffects();
    }

    // Animation au scroll
    initScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observer tous les éléments avec la classe animate-on-scroll
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    }

    // Système de particules
    initParticleSystem() {
        this.createParticleContainer();
        this.generateParticles();
        this.animateParticles();
    }

    createParticleContainer() {
        const container = document.createElement('div');
        container.className = 'particles-container';
        container.id = 'particles-container';
        document.body.appendChild(container);
    }

    generateParticles() {
        const container = document.getElementById('particles-container');
        if (!container) return;

        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Position aléatoire
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
            
            // Taille aléatoire
            const size = Math.random() * 3 + 1;
            particle.style.width = size + 'px';
            particle.style.height = size + 'px';
            
            container.appendChild(particle);
            this.particles.push(particle);
        }
    }

    animateParticles() {
        // Les particules sont animées via CSS
        // Cette méthode peut être étendue pour des animations plus complexes
    }

    // Effets de glitch
    initGlitchEffects() {
        const glitchElements = document.querySelectorAll('.military-glitch');
        
        glitchElements.forEach(element => {
            // Ajouter l'attribut data-text pour l'effet glitch
            element.setAttribute('data-text', element.textContent);
            
            // Déclencher l'effet aléatoirement
            setInterval(() => {
                if (Math.random() < 0.1) { // 10% de chance
                    this.triggerGlitch(element);
                }
            }, 2000);
        });
    }

    triggerGlitch(element) {
        element.style.animation = 'none';
        setTimeout(() => {
            element.style.animation = 'glitch 0.3s';
        }, 10);
    }

    // Effets de hover militaires
    initHoverEffects() {
        // Effet de scan sur les cartes
        document.querySelectorAll('.card, .stat-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                this.addScanEffect(card);
            });
            
            card.addEventListener('mouseleave', () => {
                this.removeScanEffect(card);
            });
        });

        // Effet de pulsation sur les boutons
        document.querySelectorAll('.btn-primary, .btn-success').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.classList.add('military-pulse');
            });
            
            btn.addEventListener('mouseleave', () => {
                btn.classList.remove('military-pulse');
            });
        });
    }

    addScanEffect(element) {
        if (!element.classList.contains('radar-scan')) {
            element.classList.add('radar-scan');
        }
    }

    removeScanEffect(element) {
        element.classList.remove('radar-scan');
    }

    // Effet de typing militaire
    typeWriter(element, text, speed = 50) {
        element.innerHTML = '';
        element.classList.add('military-typing');
        
        let i = 0;
        const timer = setInterval(() => {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
            } else {
                clearInterval(timer);
                element.classList.remove('military-typing');
            }
        }, speed);
    }

    // Effet de chargement militaire
    showMilitaryLoading(element) {
        element.classList.add('military-loading');
        
        // Créer l'indicateur de chargement
        const loader = document.createElement('div');
        loader.className = 'military-loader';
        loader.innerHTML = `
            <div class="loader-text">CHARGEMENT...</div>
            <div class="loader-bar">
                <div class="loader-progress"></div>
            </div>
        `;
        
        element.appendChild(loader);
        
        return loader;
    }

    hideMilitaryLoading(element) {
        element.classList.remove('military-loading');
        const loader = element.querySelector('.military-loader');
        if (loader) {
            loader.remove();
        }
    }

    // Effet de notification militaire
    showMilitaryNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `military-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">SYSTÈME</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Styles inline pour la notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, var(--bg-military), var(--bg-military-light));
            border: 2px solid var(--accent-color);
            border-radius: var(--radius-md);
            padding: 1rem;
            color: var(--text-white);
            box-shadow: var(--shadow-military);
            z-index: 10000;
            min-width: 300px;
            animation: slideInFromRight 0.5s ease-out;
        `;

        document.body.appendChild(notification);

        // Fermer la notification
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.style.animation = 'slideOutToRight 0.5s ease-in';
            setTimeout(() => notification.remove(), 500);
        });

        // Auto-fermeture
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutToRight 0.5s ease-in';
                setTimeout(() => notification.remove(), 500);
            }
        }, duration);
    }

    // Effet de zoom militaire sur les images
    initImageZoom() {
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('mouseenter', () => {
                img.style.transform = 'scale(1.05)';
                img.style.filter = 'brightness(1.1) contrast(1.1)';
                img.style.transition = 'all 0.3s ease';
            });
            
            img.addEventListener('mouseleave', () => {
                img.style.transform = 'scale(1)';
                img.style.filter = 'brightness(1) contrast(1)';
            });
        });
    }

    // Nettoyage
    destroy() {
        // Supprimer les particules
        const container = document.getElementById('particles-container');
        if (container) {
            container.remove();
        }
        
        // Nettoyer les références
        this.particles = [];
    }
}

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    window.militaryAnimations = new MilitaryAnimations();
});

// Fonctions globales
window.showMilitaryNotification = function(message, type, duration) {
    if (window.militaryAnimations) {
        window.militaryAnimations.showMilitaryNotification(message, type, duration);
    }
};

window.typeWriter = function(element, text, speed) {
    if (window.militaryAnimations) {
        window.militaryAnimations.typeWriter(element, text, speed);
    }
};
