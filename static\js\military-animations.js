/**
 * Animations Militaires Avancées
 * Gestion des effets visuels et animations pour le thème militaire
 */

class MilitaryAnimations {
    constructor() {
        this.init();
    }

    init() {
        this.initScrollAnimations();
        this.initHoverEffects();
        // Particules et glitch supprimés pour une interface plus propre
    }

    // Animation au scroll
    initScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observer tous les éléments avec la classe animate-on-scroll
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    }

    // Système de particules supprimé pour une interface plus propre

    // Effets de glitch supprimés pour une interface plus stable

    // Effets de hover simplifiés
    initHoverEffects() {
        // Effet subtil sur les cartes (seulement élévation)
        document.querySelectorAll('.card, .stat-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
                card.style.transition = 'all 0.2s ease';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });

        // Effet simple sur les boutons (pas de pulsation)
        document.querySelectorAll('.btn-primary, .btn-success').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'scale(1.02)';
                btn.style.transition = 'all 0.2s ease';
            });

            btn.addEventListener('mouseleave', () => {
                btn.style.transform = 'scale(1)';
            });
        });
    }

    // Effet de typing militaire
    typeWriter(element, text, speed = 50) {
        element.innerHTML = '';
        element.classList.add('military-typing');
        
        let i = 0;
        const timer = setInterval(() => {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
            } else {
                clearInterval(timer);
                element.classList.remove('military-typing');
            }
        }, speed);
    }

    // Effet de chargement militaire
    showMilitaryLoading(element) {
        element.classList.add('military-loading');
        
        // Créer l'indicateur de chargement
        const loader = document.createElement('div');
        loader.className = 'military-loader';
        loader.innerHTML = `
            <div class="loader-text">CHARGEMENT...</div>
            <div class="loader-bar">
                <div class="loader-progress"></div>
            </div>
        `;
        
        element.appendChild(loader);
        
        return loader;
    }

    hideMilitaryLoading(element) {
        element.classList.remove('military-loading');
        const loader = element.querySelector('.military-loader');
        if (loader) {
            loader.remove();
        }
    }

    // Effet de notification militaire
    showMilitaryNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `military-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">SYSTÈME</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Styles inline pour la notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, var(--bg-military), var(--bg-military-light));
            border: 2px solid var(--accent-color);
            border-radius: var(--radius-md);
            padding: 1rem;
            color: var(--text-white);
            box-shadow: var(--shadow-military);
            z-index: 10000;
            min-width: 300px;
            animation: slideInFromRight 0.5s ease-out;
        `;

        document.body.appendChild(notification);

        // Fermer la notification
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.style.animation = 'slideOutToRight 0.5s ease-in';
            setTimeout(() => notification.remove(), 500);
        });

        // Auto-fermeture
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutToRight 0.5s ease-in';
                setTimeout(() => notification.remove(), 500);
            }
        }, duration);
    }

    // Effet de zoom militaire sur les images
    initImageZoom() {
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('mouseenter', () => {
                img.style.transform = 'scale(1.05)';
                img.style.filter = 'brightness(1.1) contrast(1.1)';
                img.style.transition = 'all 0.3s ease';
            });
            
            img.addEventListener('mouseleave', () => {
                img.style.transform = 'scale(1)';
                img.style.filter = 'brightness(1) contrast(1)';
            });
        });
    }

    // Nettoyage
    destroy() {
        // Supprimer les particules
        const container = document.getElementById('particles-container');
        if (container) {
            container.remove();
        }
        
        // Nettoyer les références
        this.particles = [];
    }
}

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    window.militaryAnimations = new MilitaryAnimations();
});

// Fonctions globales
window.showMilitaryNotification = function(message, type, duration) {
    if (window.militaryAnimations) {
        window.militaryAnimations.showMilitaryNotification(message, type, duration);
    }
};

window.typeWriter = function(element, text, speed) {
    if (window.militaryAnimations) {
        window.militaryAnimations.typeWriter(element, text, speed);
    }
};
