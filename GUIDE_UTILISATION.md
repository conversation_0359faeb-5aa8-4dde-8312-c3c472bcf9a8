# 📖 Guide d'Utilisation - StageManager

## 🚀 Démarrage Rapide

### 1. Lancement de l'Application
```bash
# Dans le dossier du projet
python app.py
```
➡️ Ouvrir `http://localhost:5000` dans le navigateur

### 2. Navigation
- **Page d'accueil** : Présentation et accès rapide
- **Tableau de bord** : Vue d'ensemble et statistiques
- **Stagiaires** : Gestion des stagiaires
- **Stages** : Gestion des stages
- **Promotions** : Gestion des promotions
- **Documents** : Génération et gestion des documents
- **Assistant IA** : Chat<PERSON> d'aide

## 👥 Gestion des Stagiaires

### ➕ Ajouter un Stagiaire
1. Aller sur la page **Stagiaires**
2. C<PERSON>r sur **"Nouveau stagiaire"**
3. Remplir le formulaire :
   - Nom, Prénom (obligatoires)
   - Email (obligatoire)
   - Téléphone, Date de naissance
   - Niveau d'étude, Spécialité
   - Adresse
4. <PERSON><PERSON><PERSON> **"Enregistrer"**

### 🔍 Rechercher des Stagiaires
- **Barre de recherche** : Taper nom, prénom ou email
- **Filtres** :
  - Statut (Actif, Inactif, Suspendu)
  - Niveau d'étude
  - Promotion
  - Date d'inscription

### 👁️ Voir les Détails
- Cliquer sur l'icône **👁️** dans la liste
- Modal avec toutes les informations
- Historique des stages

### ✏️ Modifier un Stagiaire
- Cliquer sur l'icône **✏️** dans la liste
- Modifier les informations
- Cliquer **"Enregistrer"**

### 📊 Exporter la Liste
1. Cliquer sur **"Exporter"**
2. Choisir le format (Excel/PDF)
3. Sélectionner une promotion (optionnel)
4. Cliquer **"Exporter"**
5. Le fichier se télécharge automatiquement

## 💼 Gestion des Stages

### ➕ Créer un Stage
1. Aller sur la page **Stages**
2. Cliquer **"Nouveau stage"**
3. Remplir le formulaire :
   - Sélectionner un stagiaire
   - Type de stage
   - Dates de début et fin
   - Unité d'affectation
   - Responsable
   - Objectifs
4. Cliquer **"Enregistrer"**

### 📅 Vues Disponibles
- **Tableau** : Liste détaillée avec filtres
- **Cartes** : Vue en cartes avec progression
- **Timeline** : Vue chronologique des stages

### 🔍 Filtres Avancés
- **Statut** : En cours, En attente, Terminé, Suspendu
- **Type** : Stage d'observation, formation, perfectionnement
- **Période** : En cours, À venir, Passées
- **Unité** : Filtrer par unité d'affectation

### 📈 Progression Automatique
- Calculée automatiquement selon les dates
- Affichage en pourcentage et barre de progression
- Mise à jour en temps réel

## 🎓 Gestion des Promotions

### ➕ Créer une Promotion
1. Aller sur la page **Promotions**
2. Cliquer **"Nouvelle promotion"**
3. Remplir :
   - Nom de la promotion
   - Année
   - Filière
   - Effectif maximum
   - Dates (optionnelles)
   - Description
4. Cliquer **"Enregistrer"**

### 📊 Statistiques
- Effectif actuel vs maximum
- Nombre de stages associés
- Progression de remplissage
- Statut de la promotion

### 👥 Gestion des Affectations
- Voir les stagiaires inscrits
- Statistiques par promotion
- Export des listes par promotion

## 📄 Génération de Documents

### 📋 Types de Documents Disponibles

#### 1. **Convention de Stage** (PDF)
1. Cliquer sur la carte **"Convention de stage"**
2. Sélectionner :
   - Stagiaire
   - Stage correspondant
   - Format PDF
3. Options :
   - Inclure zones de signature
   - Ajouter filigrane
   - Envoi automatique par email
4. Cliquer **"Générer le document"**
5. **Téléchargement automatique** du PDF

#### 2. **Attestation de Stage** (PDF)
1. Cliquer sur **"Attestation de stage"**
2. Sélectionner stagiaire et stage
3. Générer → **PDF téléchargé automatiquement**

#### 3. **Liste des Stagiaires** (Excel/PDF)
1. Cliquer sur **"Liste des stagiaires"**
2. Choisir format (Excel ou PDF)
3. Sélectionner promotion (optionnel)
4. Générer → **Fichier téléchargé**

### 📁 Gestion des Documents Générés
- **Liste** de tous les documents créés
- **Téléchargement** individuel ou en lot
- **Suppression** avec nettoyage des fichiers
- **Filtres** par type, format, date

### 📦 Téléchargement en Lot
1. Cocher les documents souhaités
2. Cliquer **"Télécharger sélectionnés"**
3. **Archive ZIP** téléchargée automatiquement

## 🤖 Assistant IA (Chatbot)

### 💬 Utilisation du Chat
1. Aller sur la page **Assistant IA**
2. Taper une question dans la zone de texte
3. Appuyer **Entrée** ou cliquer **➤**

### 🎯 Suggestions Rapides
Cliquer sur les boutons de suggestion :
- 📊 **Statistiques des stages**
- 📝 **Générer une convention**
- ⏰ **Échéances de la semaine**
- ❓ **Aide sur l'application**
- 📋 **Exporter des listes**
- 🚨 **Voir les alertes**

### ⚙️ Paramètres du Chat
1. Cliquer sur l'icône **⚙️**
2. Configurer :
   - Mode de réponse (Détaillé/Concis/Technique)
   - Langue
   - Notifications sonores
   - Sauvegarde des conversations

### 📤 Export de Conversation
- Cliquer sur l'icône **📤**
- Fichier JSON téléchargé avec l'historique

## 🔍 Recherche Globale

### 🔎 Utilisation
1. Cliquer dans la **barre de recherche** (en haut)
2. Taper au moins 2 caractères
3. **Résultats instantanés** s'affichent
4. Cliquer sur un résultat pour naviguer

### 📋 Types de Résultats
- **👤 Stagiaires** : Nom, prénom, email
- **💼 Stages** : Unité, responsable
- **🎓 Promotions** : Nom, filière, année
- **📄 Documents** : Nom de fichier

## ⚡ Actions Rapides (Bouton FAB)

### 🎯 Bouton Flottant
- **Position** : Coin inférieur droit
- **Clic** : Ouvre le menu d'actions rapides

### 🚀 Actions Disponibles
- **➕ Nouveau stagiaire** → Ouvre le formulaire
- **💼 Nouveau stage** → Ouvre le formulaire
- **🎓 Nouvelle promotion** → Ouvre le formulaire
- **📄 Générer document** → Ouvre l'assistant

## 📊 Tableau de Bord

### 📈 Graphiques Interactifs
- **Répartition des stages** par statut
- **Évolution mensuelle** des inscriptions
- **Statistiques par promotion**
- **Hover** pour détails

### 🔔 Alertes et Notifications
- **Stages se terminant** bientôt
- **Documents manquants**
- **Échéances importantes**

### 📋 Activités Récentes
- **Dernières inscriptions**
- **Stages créés**
- **Documents générés**

## 💡 Conseils d'Utilisation

### ⌨️ Raccourcis Clavier
- **Ctrl + K** : Effacer conversation chatbot
- **Ctrl + /** : Afficher/masquer aide chatbot
- **Entrée** : Envoyer message chatbot
- **Shift + Entrée** : Nouvelle ligne dans le chat

### 📱 Responsive Design
- **Mobile** : Menu hamburger, interface adaptée
- **Tablette** : Colonnes ajustées
- **Desktop** : Interface complète

### 🎨 Interface
- **Thème moderne** avec animations fluides
- **Indicateurs visuels** pour toutes les actions
- **Messages de confirmation** pour les opérations importantes

## 🔧 Dépannage

### ❌ Problèmes Courants

#### Serveur ne démarre pas
```bash
# Vérifier les dépendances
pip install flask flask-cors reportlab pandas openpyxl
```

#### Documents ne se génèrent pas
- Vérifier que les dossiers `uploads/` existent
- Permissions d'écriture sur le dossier

#### Recherche ne fonctionne pas
- Vérifier que le serveur Flask est démarré
- Ouvrir la console développeur (F12) pour voir les erreurs

### 📞 Support
- Consulter les logs dans la console
- Vérifier le fichier `test_functionality.py` pour diagnostics
- Redémarrer le serveur en cas de problème

---

## 🎉 Profitez de StageManager !

L'application est maintenant **complètement fonctionnelle** avec toutes les fonctionnalités opérationnelles. Explorez toutes les possibilités et n'hésitez pas à tester chaque fonctionnalité ! 🚀
