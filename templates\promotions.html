{% extends "base.html" %}

{% block title %}Gestion des Promotions - StageManager{% endblock %}
{% block page_title %}Gestion des Promotions{% endblock %}

{% block content %}
<!-- Header avec actions -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-header-left">
            <h2 class="page-title">Promotions</h2>
            <p class="page-description">Organisez et gérez les promotions de stagiaires</p>
        </div>
        <div class="page-header-right">
            <button class="btn btn-secondary" id="exportPromotionsBtn">
                <i class="fas fa-download"></i>
                Exporter
            </button>
            <button class="btn btn-primary" id="addPromotionBtn">
                <i class="fas fa-plus"></i>
                Nouvelle promotion
            </button>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-3">
        <div class="stat-card">
            <div class="stat-icon bg-primary">
                <i class="fas fa-layer-group"></i>
            </div>
            <div class="stat-content">
                <h3 id="totalPromotions">0</h3>
                <p>Total promotions</p>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="stat-card">
            <div class="stat-icon bg-success">
                <i class="fas fa-play-circle"></i>
            </div>
            <div class="stat-content">
                <h3 id="promotionsActives">0</h3>
                <p>Actives</p>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="stat-card">
            <div class="stat-icon bg-warning">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3 id="totalStagiairesPromotions">0</h3>
                <p>Stagiaires inscrits</p>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="stat-card">
            <div class="stat-icon bg-info">
                <i class="fas fa-calendar"></i>
            </div>
            <div class="stat-content">
                <h3 id="promotionsAnnee">0</h3>
                <p>Cette année</p>
            </div>
        </div>
    </div>
</div>

<!-- Filtres et recherche -->
<div class="filters-section">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-4">
                    <div class="form-group">
                        <label class="form-label">Rechercher</label>
                        <div class="search-input">
                            <input type="text" class="form-control" id="searchPromotions" placeholder="Nom, filière, année...">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">Statut</label>
                        <select class="form-select" id="filterStatutPromotion">
                            <option value="">Tous</option>
                            <option value="active">Active</option>
                            <option value="terminee">Terminée</option>
                            <option value="planifiee">Planifiée</option>
                        </select>
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">Année</label>
                        <select class="form-select" id="filterAnnee">
                            <option value="">Toutes</option>
                        </select>
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">Filière</label>
                        <input type="text" class="form-control" id="filterFiliere" placeholder="Filière">
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-secondary w-100" id="resetPromotionFilters">
                            <i class="fas fa-undo"></i>
                            Réinitialiser
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Liste des promotions -->
<div class="promotions-section">
    <div class="card">
        <div class="card-header">
            <div class="card-header-content">
                <h5 class="card-title">Liste des promotions</h5>
                <div class="card-actions">
                    <div class="view-toggle">
                        <button class="btn btn-sm btn-secondary" id="promotionTableViewBtn">
                            <i class="fas fa-table"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary active" id="promotionCardViewBtn">
                            <i class="fas fa-th-large"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Vue tableau -->
            <div class="table-view" id="promotionTableView" style="display: none;">
                <div class="table-container">
                    <table class="table" id="promotionsTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAllPromotions">
                                </th>
                                <th>Nom</th>
                                <th>Année</th>
                                <th>Filière</th>
                                <th>Période</th>
                                <th>Effectif</th>
                                <th>Statut</th>
                                <th>Date création</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="promotionsTableBody">
                            <!-- Les données seront chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Vue cartes -->
            <div class="card-view" id="promotionCardView">
                <div class="promotions-grid" id="promotionsGrid">
                    <!-- Les cartes seront chargées dynamiquement -->
                </div>
            </div>
            
            <!-- Pagination -->
            <div class="pagination-section">
                <div class="pagination-info">
                    <span id="promotionPaginationInfo">Affichage de 1 à 10 sur 0 promotions</span>
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-sm btn-secondary" id="promotionPrevPage" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="pagination-pages" id="promotionPaginationPages">
                        <!-- Les numéros de page seront générés dynamiquement -->
                    </div>
                    <button class="btn btn-sm btn-secondary" id="promotionNextPage" disabled>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Nouvelle/Modifier Promotion -->
<div class="modal" id="promotionModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="promotionModalTitle">Nouvelle promotion</h5>
                <button class="modal-close" id="closePromotionModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="promotionForm">
                    <input type="hidden" id="promotionId">
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Nom de la promotion *</label>
                                <input type="text" class="form-control" id="nomPromotion" required placeholder="Ex: Promo 2024-A">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Année *</label>
                                <input type="number" class="form-control" id="anneePromotion" required min="2020" max="2030">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Filière *</label>
                                <input type="text" class="form-control" id="filierePromotion" required placeholder="Ex: Informatique, Gestion">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Effectif maximum</label>
                                <input type="number" class="form-control" id="effectifMax" min="1" max="200" value="50">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Date de début</label>
                                <input type="date" class="form-control" id="dateDebutPromotion">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Date de fin</label>
                                <input type="date" class="form-control" id="dateFinPromotion">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" id="descriptionPromotion" rows="3" placeholder="Description de la promotion, objectifs, particularités..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Statut</label>
                        <select class="form-select" id="statutPromotion">
                            <option value="planifiee">Planifiée</option>
                            <option value="active">Active</option>
                            <option value="terminee">Terminée</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelPromotion">Annuler</button>
                <button class="btn btn-primary" id="savePromotion">
                    <i class="fas fa-save"></i>
                    Enregistrer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Détails Promotion -->
<div class="modal" id="detailsPromotionModal">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails de la promotion</h5>
                <button class="modal-close" id="closePromotionDetailsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="promotionDetails">
                    <!-- Le contenu sera chargé dynamiquement -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.promotions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.promotion-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.promotion-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.promotion-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.promotion-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.promotion-info h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.125rem;
}

.promotion-meta {
    font-size: 0.875rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.promotion-year {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
}

.promotion-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin: 1rem 0;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.promotion-stat {
    text-align: center;
}

.promotion-stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.promotion-stat-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.promotion-details {
    margin-bottom: 1rem;
}

.promotion-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.promotion-detail label {
    font-weight: 500;
    color: var(--text-secondary);
}

.promotion-progress {
    margin: 1rem 0;
}

.promotion-progress-bar {
    width: 100%;
    height: 0.5rem;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.promotion-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), #34d399);
    transition: width var(--transition-normal);
}

.promotion-progress-text {
    font-size: 0.75rem;
    color: var(--text-muted);
    display: flex;
    justify-content: space-between;
}

.promotion-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
}

.promotion-status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    border: 2px solid var(--bg-primary);
}

.promotion-status-indicator.active {
    background-color: var(--success-color);
}

.promotion-status-indicator.terminee {
    background-color: var(--text-muted);
}

.promotion-status-indicator.planifiee {
    background-color: var(--warning-color);
}

.promotion-timeline {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0.5rem 0;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.promotion-timeline i {
    color: var(--primary-color);
}

.promotion-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 1rem 0;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    border-left: 3px solid var(--primary-color);
}

.stagiaires-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 0.5rem;
}

.stagiaire-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.stagiaire-item:hover {
    background-color: var(--bg-secondary);
}

.stagiaire-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
}

.stagiaire-info {
    flex: 1;
}

.stagiaire-name {
    font-weight: 500;
    font-size: 0.875rem;
    margin-bottom: 0.125rem;
}

.stagiaire-email {
    font-size: 0.75rem;
    color: var(--text-muted);
}

@media (max-width: 768px) {
    .promotions-grid {
        grid-template-columns: 1fr;
    }
    
    .promotion-stats {
        grid-template-columns: 1fr;
    }
    
    .promotion-header {
        flex-direction: column;
        gap: 0.5rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/promotions.js') }}"></script>
{% endblock %}
