// Gestion des stagiaires
class StagiairesManager {
    constructor() {
        this.stagiaires = [];
        this.filteredStagiaires = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.currentView = 'table';
        this.filters = {
            search: '',
            statut: '',
            promotion: '',
            niveau: ''
        };
        
        this.init();
        this.bindEvents();
        this.loadStagiaires();
    }

    init() {
        // Éléments DOM
        this.tableView = document.getElementById('tableView');
        this.cardView = document.getElementById('cardView');
        this.tableViewBtn = document.getElementById('tableViewBtn');
        this.cardViewBtn = document.getElementById('cardViewBtn');
        this.stagiaireModal = document.getElementById('stagiaireModal');
        this.detailsModal = document.getElementById('detailsStagiaireModal');
        this.stagiaireForm = document.getElementById('stagiaireForm');
        
        // Filtres
        this.searchInput = document.getElementById('searchStagiaires');
        this.filterStatut = document.getElementById('filterStatut');
        this.filterPromotion = document.getElementById('filterPromotion');
        this.filterNiveau = document.getElementById('filterNiveau');
    }

    bindEvents() {
        // Boutons de vue
        this.tableViewBtn?.addEventListener('click', () => this.switchView('table'));
        this.cardViewBtn?.addEventListener('click', () => this.switchView('card'));
        
        // Modal nouveau stagiaire
        document.getElementById('addStagiaireBtn')?.addEventListener('click', () => this.openStagiaireModal());
        document.getElementById('closeStagiaireModal')?.addEventListener('click', () => this.closeStagiaireModal());
        document.getElementById('cancelStagiaire')?.addEventListener('click', () => this.closeStagiaireModal());
        document.getElementById('saveStagiaire')?.addEventListener('click', () => this.saveStagiaire());
        
        // Modal détails
        document.getElementById('closeDetailsModal')?.addEventListener('click', () => this.closeDetailsModal());
        
        // Filtres
        this.searchInput?.addEventListener('input', (e) => {
            this.filters.search = e.target.value;
            this.debounce(() => this.applyFilters(), 300)();
        });
        
        this.filterStatut?.addEventListener('change', (e) => {
            this.filters.statut = e.target.value;
            this.applyFilters();
        });
        
        this.filterPromotion?.addEventListener('change', (e) => {
            this.filters.promotion = e.target.value;
            this.applyFilters();
        });
        
        this.filterNiveau?.addEventListener('change', (e) => {
            this.filters.niveau = e.target.value;
            this.applyFilters();
        });
        
        // Reset filtres
        document.getElementById('resetFilters')?.addEventListener('click', () => this.resetFilters());
        
        // Pagination
        document.getElementById('prevPage')?.addEventListener('click', () => this.previousPage());
        document.getElementById('nextPage')?.addEventListener('click', () => this.nextPage());
        
        // Upload photo
        document.getElementById('photo')?.addEventListener('change', (e) => this.handlePhotoUpload(e));
        
        // Fermer modals en cliquant à l'extérieur
        this.stagiaireModal?.addEventListener('click', (e) => {
            if (e.target === this.stagiaireModal) this.closeStagiaireModal();
        });
        
        this.detailsModal?.addEventListener('click', (e) => {
            if (e.target === this.detailsModal) this.closeDetailsModal();
        });
    }

    async loadStagiaires() {
        try {
            window.stageManager.showLoading();
            const response = await window.stageManager.get('/stagiaires');
            this.stagiaires = response;
            this.applyFilters();
        } catch (error) {
            console.error('Erreur lors du chargement des stagiaires:', error);
            window.stageManager.showToast('Erreur lors du chargement des stagiaires', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    applyFilters() {
        this.filteredStagiaires = this.stagiaires.filter(stagiaire => {
            const matchSearch = !this.filters.search || 
                stagiaire.nom.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                stagiaire.prenom.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                stagiaire.email.toLowerCase().includes(this.filters.search.toLowerCase());
            
            const matchStatut = !this.filters.statut || stagiaire.statut === this.filters.statut;
            const matchNiveau = !this.filters.niveau || stagiaire.niveau_etude === this.filters.niveau;
            
            return matchSearch && matchStatut && matchNiveau;
        });
        
        this.currentPage = 1;
        this.renderStagiaires();
        this.updatePagination();
    }

    renderStagiaires() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageData = this.filteredStagiaires.slice(startIndex, endIndex);
        
        if (this.currentView === 'table') {
            this.renderTableView(pageData);
        } else {
            this.renderCardView(pageData);
        }
    }

    renderTableView(stagiaires) {
        const tbody = document.getElementById('stagiairesTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = stagiaires.map(stagiaire => `
            <tr>
                <td>
                    <input type="checkbox" value="${stagiaire.id}">
                </td>
                <td>
                    <div class="user-avatar">
                        ${stagiaire.photo ? 
                            `<img src="${stagiaire.photo}" alt="${stagiaire.nom}">` :
                            `<div class="avatar-placeholder">${stagiaire.nom.charAt(0)}${stagiaire.prenom.charAt(0)}</div>`
                        }
                    </div>
                </td>
                <td>
                    <div class="user-info">
                        <strong>${stagiaire.nom} ${stagiaire.prenom}</strong>
                    </div>
                </td>
                <td>${stagiaire.email}</td>
                <td>${stagiaire.telephone || '-'}</td>
                <td>${stagiaire.niveau_etude || '-'}</td>
                <td>${window.getStatusBadge(stagiaire.statut)}</td>
                <td>${window.formatDate(stagiaire.date_inscription)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="stagiairesManager.viewStagiaire('${stagiaire.id}')" title="Voir détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="stagiairesManager.editStagiaire('${stagiaire.id}')" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="stagiairesManager.deleteStagiaire('${stagiaire.id}')" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderCardView(stagiaires) {
        const grid = document.getElementById('stagiairesGrid');
        if (!grid) return;
        
        grid.innerHTML = stagiaires.map(stagiaire => `
            <div class="stagiaire-card">
                <div class="stagiaire-header">
                    <div class="stagiaire-avatar">
                        ${stagiaire.photo ? 
                            `<img src="${stagiaire.photo}" alt="${stagiaire.nom}">` :
                            `${stagiaire.nom.charAt(0)}${stagiaire.prenom.charAt(0)}`
                        }
                    </div>
                    <div class="stagiaire-info">
                        <h6>${stagiaire.nom} ${stagiaire.prenom}</h6>
                        <p>${stagiaire.email}</p>
                    </div>
                    <div class="stagiaire-status">
                        ${window.getStatusBadge(stagiaire.statut)}
                    </div>
                </div>
                
                <div class="stagiaire-details">
                    <div class="stagiaire-detail">
                        <span>Téléphone:</span>
                        <span>${stagiaire.telephone || '-'}</span>
                    </div>
                    <div class="stagiaire-detail">
                        <span>Niveau:</span>
                        <span>${stagiaire.niveau_etude || '-'}</span>
                    </div>
                    <div class="stagiaire-detail">
                        <span>Inscription:</span>
                        <span>${window.formatDate(stagiaire.date_inscription)}</span>
                    </div>
                </div>
                
                <div class="stagiaire-actions">
                    <button class="btn btn-sm btn-secondary" onclick="stagiairesManager.viewStagiaire('${stagiaire.id}')" title="Voir détails">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="stagiairesManager.editStagiaire('${stagiaire.id}')" title="Modifier">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="stagiairesManager.deleteStagiaire('${stagiaire.id}')" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    switchView(view) {
        this.currentView = view;
        
        if (view === 'table') {
            this.tableView.style.display = 'block';
            this.cardView.style.display = 'none';
            this.tableViewBtn.classList.add('active');
            this.cardViewBtn.classList.remove('active');
        } else {
            this.tableView.style.display = 'none';
            this.cardView.style.display = 'block';
            this.tableViewBtn.classList.remove('active');
            this.cardViewBtn.classList.add('active');
        }
        
        this.renderStagiaires();
    }

    updatePagination() {
        const totalPages = Math.ceil(this.filteredStagiaires.length / this.itemsPerPage);
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, this.filteredStagiaires.length);
        
        // Mise à jour des informations
        const paginationInfo = document.getElementById('paginationInfo');
        if (paginationInfo) {
            paginationInfo.textContent = `Affichage de ${startItem} à ${endItem} sur ${this.filteredStagiaires.length} stagiaires`;
        }
        
        // Mise à jour des boutons
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');
        
        if (prevBtn) prevBtn.disabled = this.currentPage === 1;
        if (nextBtn) nextBtn.disabled = this.currentPage === totalPages || totalPages === 0;
        
        // Génération des numéros de page
        this.generatePageNumbers(totalPages);
    }

    generatePageNumbers(totalPages) {
        const pagesContainer = document.getElementById('paginationPages');
        if (!pagesContainer) return;
        
        pagesContainer.innerHTML = '';
        
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.addEventListener('click', () => this.goToPage(i));
                pagesContainer.appendChild(pageBtn);
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.className = 'pagination-ellipsis';
                pagesContainer.appendChild(ellipsis);
            }
        }
    }

    goToPage(page) {
        this.currentPage = page;
        this.renderStagiaires();
        this.updatePagination();
    }

    previousPage() {
        if (this.currentPage > 1) {
            this.goToPage(this.currentPage - 1);
        }
    }

    nextPage() {
        const totalPages = Math.ceil(this.filteredStagiaires.length / this.itemsPerPage);
        if (this.currentPage < totalPages) {
            this.goToPage(this.currentPage + 1);
        }
    }

    resetFilters() {
        this.filters = {
            search: '',
            statut: '',
            promotion: '',
            niveau: ''
        };
        
        // Reset des inputs
        if (this.searchInput) this.searchInput.value = '';
        if (this.filterStatut) this.filterStatut.value = '';
        if (this.filterPromotion) this.filterPromotion.value = '';
        if (this.filterNiveau) this.filterNiveau.value = '';
        
        this.applyFilters();
    }

    openStagiaireModal(stagiaire = null) {
        const modal = this.stagiaireModal;
        const title = document.getElementById('stagiaireModalTitle');
        
        if (stagiaire) {
            title.textContent = 'Modifier le stagiaire';
            this.fillStagiaireForm(stagiaire);
        } else {
            title.textContent = 'Nouveau stagiaire';
            this.stagiaireForm.reset();
            document.getElementById('stagiaireId').value = '';
        }
        
        modal.classList.add('show');
    }

    closeStagiaireModal() {
        this.stagiaireModal.classList.remove('show');
    }

    closeDetailsModal() {
        this.detailsModal.classList.remove('show');
    }

    fillStagiaireForm(stagiaire) {
        document.getElementById('stagiaireId').value = stagiaire.id;
        document.getElementById('nom').value = stagiaire.nom;
        document.getElementById('prenom').value = stagiaire.prenom;
        document.getElementById('email').value = stagiaire.email;
        document.getElementById('telephone').value = stagiaire.telephone || '';
        document.getElementById('dateNaissance').value = stagiaire.date_naissance || '';
        document.getElementById('niveauEtude').value = stagiaire.niveau_etude || '';
        document.getElementById('adresse').value = stagiaire.adresse || '';
        document.getElementById('specialite').value = stagiaire.specialite || '';
    }

    async saveStagiaire() {
        const formData = new FormData(this.stagiaireForm);
        const stagiaireId = document.getElementById('stagiaireId').value;
        
        const data = {
            nom: document.getElementById('nom').value,
            prenom: document.getElementById('prenom').value,
            email: document.getElementById('email').value,
            telephone: document.getElementById('telephone').value,
            date_naissance: document.getElementById('dateNaissance').value,
            niveau_etude: document.getElementById('niveauEtude').value,
            adresse: document.getElementById('adresse').value,
            specialite: document.getElementById('specialite').value
        };
        
        try {
            window.stageManager.showLoading();
            
            if (stagiaireId) {
                await window.stageManager.put(`/stagiaires/${stagiaireId}`, data);
                window.stageManager.showToast('Stagiaire modifié avec succès', 'success');
            } else {
                await window.stageManager.post('/stagiaires', data);
                window.stageManager.showToast('Stagiaire créé avec succès', 'success');
            }
            
            this.closeStagiaireModal();
            await this.loadStagiaires();
            
        } catch (error) {
            console.error('Erreur lors de la sauvegarde:', error);
            window.stageManager.showToast('Erreur lors de la sauvegarde', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    async editStagiaire(id) {
        const stagiaire = this.stagiaires.find(s => s.id === id);
        if (stagiaire) {
            this.openStagiaireModal(stagiaire);
        }
    }

    async viewStagiaire(id) {
        const stagiaire = this.stagiaires.find(s => s.id === id);
        if (stagiaire) {
            this.showStagiaireDetails(stagiaire);
        }
    }

    showStagiaireDetails(stagiaire) {
        const detailsContainer = document.getElementById('stagiaireDetails');
        detailsContainer.innerHTML = `
            <div class="stagiaire-profile">
                <div class="profile-header">
                    <div class="profile-avatar">
                        ${stagiaire.photo ? 
                            `<img src="${stagiaire.photo}" alt="${stagiaire.nom}">` :
                            `<div class="avatar-placeholder">${stagiaire.nom.charAt(0)}${stagiaire.prenom.charAt(0)}</div>`
                        }
                    </div>
                    <div class="profile-info">
                        <h3>${stagiaire.nom} ${stagiaire.prenom}</h3>
                        <p>${stagiaire.email}</p>
                        ${window.getStatusBadge(stagiaire.statut)}
                    </div>
                </div>
                
                <div class="profile-details">
                    <div class="detail-section">
                        <h5>Informations personnelles</h5>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Téléphone:</label>
                                <span>${stagiaire.telephone || '-'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Date de naissance:</label>
                                <span>${window.formatDate(stagiaire.date_naissance) || '-'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Adresse:</label>
                                <span>${stagiaire.adresse || '-'}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-section">
                        <h5>Formation</h5>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Niveau d'étude:</label>
                                <span>${stagiaire.niveau_etude || '-'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Spécialité:</label>
                                <span>${stagiaire.specialite || '-'}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-section">
                        <h5>Informations système</h5>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Date d'inscription:</label>
                                <span>${window.formatDateTime(stagiaire.date_inscription)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Statut:</label>
                                <span>${stagiaire.statut}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.detailsModal.classList.add('show');
    }

    async deleteStagiaire(id) {
        if (!confirm('Êtes-vous sûr de vouloir supprimer ce stagiaire ?')) {
            return;
        }
        
        try {
            window.stageManager.showLoading();
            await window.stageManager.delete(`/stagiaires/${id}`);
            window.stageManager.showToast('Stagiaire supprimé avec succès', 'success');
            await this.loadStagiaires();
        } catch (error) {
            console.error('Erreur lors de la suppression:', error);
            window.stageManager.showToast('Erreur lors de la suppression', 'error');
        } finally {
            window.stageManager.hideLoading();
        }
    }

    handlePhotoUpload(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.getElementById('photoPreview');
                preview.innerHTML = `<img src="${e.target.result}" alt="Aperçu">`;
            };
            reader.readAsDataURL(file);
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialiser le gestionnaire des stagiaires
document.addEventListener('DOMContentLoaded', () => {
    window.stagiairesManager = new StagiairesManager();
});
