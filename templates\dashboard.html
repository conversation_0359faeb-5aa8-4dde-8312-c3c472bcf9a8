{% extends "base.html" %}

{% block title %}Tableau de bord - Gestion des Stages{% endblock %}
{% block page_title %}Tableau de bord{% endblock %}

{% block content %}
<!-- Statistiques principales -->
<div class="row mb-4">
    <div class="col-3">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="text-primary mb-1" id="total-stagiaires">0</h3>
                        <p class="text-muted mb-0">Stagiaires</p>
                    </div>
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-trend mt-2">
                    <span class="text-success">
                        <i class="fas fa-arrow-up"></i> +12%
                    </span>
                    <span class="text-muted">ce mois</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-3">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="text-success mb-1" id="stages-actifs">0</h3>
                        <p class="text-muted mb-0">Stages actifs</p>
                    </div>
                    <div class="stat-icon bg-success">
                        <i class="fas fa-briefcase"></i>
                    </div>
                </div>
                <div class="stat-trend mt-2">
                    <span class="text-success">
                        <i class="fas fa-arrow-up"></i> +8%
                    </span>
                    <span class="text-muted">ce mois</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-3">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="text-warning mb-1" id="total-promotions">0</h3>
                        <p class="text-muted mb-0">Promotions</p>
                    </div>
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
                <div class="stat-trend mt-2">
                    <span class="text-success">
                        <i class="fas fa-arrow-up"></i> +3%
                    </span>
                    <span class="text-muted">ce mois</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-3">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="text-info mb-1" id="total-stages">0</h3>
                        <p class="text-muted mb-0">Total stages</p>
                    </div>
                    <div class="stat-icon bg-info">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="stat-trend mt-2">
                    <span class="text-success">
                        <i class="fas fa-arrow-up"></i> +15%
                    </span>
                    <span class="text-muted">ce mois</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graphiques et analyses -->
<div class="row mb-4">
    <div class="col-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Évolution des stages</h5>
                <div class="card-actions">
                    <select class="form-select form-select-sm" id="chartPeriod">
                        <option value="7">7 derniers jours</option>
                        <option value="30" selected>30 derniers jours</option>
                        <option value="90">3 derniers mois</option>
                        <option value="365">12 derniers mois</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <canvas id="stagesChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Répartition par type</h5>
            </div>
            <div class="card-body">
                <canvas id="typesChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Activités récentes et alertes -->
<div class="row">
    <div class="col-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Activités récentes</h5>
                <a href="#" class="btn btn-sm btn-secondary">Voir tout</a>
            </div>
            <div class="card-body">
                <div class="activity-list" id="activityList">
                    <div class="activity-item">
                        <div class="activity-icon bg-success">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <p class="activity-text">Nouveau stagiaire inscrit: <strong>Marie Dubois</strong></p>
                            <span class="activity-time">Il y a 2 heures</span>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon bg-primary">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="activity-content">
                            <p class="activity-text">Stage démarré pour <strong>Jean Martin</strong></p>
                            <span class="activity-time">Il y a 4 heures</span>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon bg-warning">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="activity-content">
                            <p class="activity-text">Document généré: Convention de stage</p>
                            <span class="activity-time">Il y a 6 heures</span>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon bg-info">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="activity-content">
                            <p class="activity-text">Nouvelle promotion créée: <strong>Promo 2024-A</strong></p>
                            <span class="activity-time">Hier</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Alertes et échéances</h5>
                <span class="badge badge-danger">3</span>
            </div>
            <div class="card-body">
                <div class="alert-list" id="alertList">
                    <div class="alert-item urgent">
                        <div class="alert-icon">
                            <i class="fas fa-exclamation-triangle text-danger"></i>
                        </div>
                        <div class="alert-content">
                            <p class="alert-text">Stage de <strong>Pierre Durand</strong> expire dans 2 jours</p>
                            <span class="alert-date">Échéance: 21/06/2025</span>
                        </div>
                        <div class="alert-actions">
                            <button class="btn btn-sm btn-primary">Prolonger</button>
                        </div>
                    </div>
                    
                    <div class="alert-item warning">
                        <div class="alert-icon">
                            <i class="fas fa-clock text-warning"></i>
                        </div>
                        <div class="alert-content">
                            <p class="alert-text">Examen prévu pour <strong>Sophie Bernard</strong></p>
                            <span class="alert-date">Date: 25/06/2025</span>
                        </div>
                        <div class="alert-actions">
                            <button class="btn btn-sm btn-secondary">Rappeler</button>
                        </div>
                    </div>
                    
                    <div class="alert-item info">
                        <div class="alert-icon">
                            <i class="fas fa-file-signature text-info"></i>
                        </div>
                        <div class="alert-content">
                            <p class="alert-text">Documents manquants pour <strong>Luc Moreau</strong></p>
                            <span class="alert-date">À compléter avant le 30/06/2025</span>
                        </div>
                        <div class="alert-actions">
                            <button class="btn btn-sm btn-warning">Relancer</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="quick-actions-fab">
    <button class="fab-main" id="quickActionsFab">
        <i class="fas fa-plus"></i>
    </button>
    <div class="fab-menu" id="quickActionsMenu">
        <a href="{{ url_for('stagiaires') }}" class="fab-item" data-tooltip="Nouveau stagiaire">
            <i class="fas fa-user-plus"></i>
        </a>
        <a href="{{ url_for('stages') }}" class="fab-item" data-tooltip="Nouveau stage">
            <i class="fas fa-briefcase"></i>
        </a>
        <a href="{{ url_for('promotions') }}" class="fab-item" data-tooltip="Nouvelle promotion">
            <i class="fas fa-layer-group"></i>
        </a>
        <a href="{{ url_for('documents') }}" class="fab-item" data-tooltip="Générer document">
            <i class="fas fa-file-alt"></i>
        </a>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.stat-trend {
    font-size: 0.875rem;
}

.card-actions {
    margin-left: auto;
}

.activity-list, .alert-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item, .alert-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light);
}

.activity-item:last-child, .alert-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.alert-icon {
    font-size: 1.125rem;
    margin-top: 0.125rem;
}

.alert-content {
    flex: 1;
}

.alert-text {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.alert-date {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.alert-actions {
    margin-left: auto;
}

.quick-actions-fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.fab-main {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border: none;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
}

.fab-main:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.fab-menu {
    position: absolute;
    bottom: 4rem;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(1rem);
    transition: all var(--transition-normal);
}

.fab-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.fab-item {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    position: relative;
}

.fab-item:hover {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.fab-item::before {
    content: attr(data-tooltip);
    position: absolute;
    right: 3.5rem;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--bg-dark);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
}

.fab-item:hover::before {
    opacity: 1;
    visibility: visible;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les graphiques
    initializeCharts();
    
    // Actions rapides FAB
    const fabMain = document.getElementById('quickActionsFab');
    const fabMenu = document.getElementById('quickActionsMenu');
    
    if (fabMain && fabMenu) {
        fabMain.addEventListener('click', function() {
            fabMenu.classList.toggle('show');
        });
        
        // Fermer le menu en cliquant ailleurs
        document.addEventListener('click', function(e) {
            if (!fabMain.contains(e.target) && !fabMenu.contains(e.target)) {
                fabMenu.classList.remove('show');
            }
        });
    }
});

function initializeCharts() {
    // Graphique d'évolution des stages
    const stagesCtx = document.getElementById('stagesChart');
    if (stagesCtx) {
        new Chart(stagesCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
                datasets: [{
                    label: 'Nouveaux stages',
                    data: [12, 19, 15, 25, 22, 30],
                    borderColor: 'rgb(79, 70, 229)',
                    backgroundColor: 'rgba(79, 70, 229, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Stages terminés',
                    data: [8, 15, 12, 18, 20, 25],
                    borderColor: 'rgb(16, 185, 129)',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // Graphique de répartition par type
    const typesCtx = document.getElementById('typesChart');
    if (typesCtx) {
        new Chart(typesCtx, {
            type: 'doughnut',
            data: {
                labels: ['Observation', 'Formation', 'Perfectionnement'],
                datasets: [{
                    data: [45, 35, 20],
                    backgroundColor: [
                        'rgb(79, 70, 229)',
                        'rgb(16, 185, 129)',
                        'rgb(245, 158, 11)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    }
}
</script>
{% endblock %}
