{% extends "base.html" %}

{% block title %}Assistant IA - StageManager{% endblock %}
{% block page_title %}Assistant IA{% endblock %}

{% block content %}
<div class="chatbot-container">
    <!-- Header du chat -->
    <div class="chat-header" style="background: var(--bg-military); border-bottom: 2px solid var(--accent-color);">
        <div class="chat-header-info">
            <div class="bot-avatar military-pulse" style="background: rgba(255, 107, 53, 0.2); border: 2px solid var(--accent-color);">
                <i class="fas fa-robot" style="color: var(--accent-color); text-shadow: 0 0 10px var(--accent-color);"></i>
            </div>
            <div class="bot-info">
                <h3 style="font-family: 'Orbitron', monospace; font-weight: 700; color: var(--text-white);">ASSISTANT TACTIQUE</h3>
                <p class="bot-status">
                    <span class="status-indicator online military-pulse"></span>
                    <span style="color: var(--accent-color); font-family: 'Orbitron', monospace; font-weight: 600;">OPÉRATIONNEL - PRÊT POUR MISSION</span>
                </p>
            </div>
        </div>
        <div class="chat-actions">
            <button class="btn btn-sm btn-secondary" id="clearChatBtn" title="Effacer la conversation">
                <i class="fas fa-trash"></i>
            </button>
            <button class="btn btn-sm btn-secondary" id="exportChatBtn" title="Exporter la conversation">
                <i class="fas fa-download"></i>
            </button>
            <button class="btn btn-sm btn-secondary" id="chatSettingsBtn" title="Paramètres">
                <i class="fas fa-cog"></i>
            </button>
        </div>
    </div>

    <!-- Zone de conversation -->
    <div class="chat-messages" id="chatMessages">
        <!-- Message de bienvenue -->
        <div class="message bot-message">
            <div class="message-avatar" style="background: var(--bg-military); border: 2px solid var(--accent-color);">
                <i class="fas fa-robot" style="color: var(--accent-color);"></i>
            </div>
            <div class="message-content">
                <div class="message-bubble military-glow" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9)); border: 1px solid var(--accent-color);">
                    <p style="font-family: 'Orbitron', monospace; font-weight: 700; color: var(--primary-color);">🎯 SYSTÈME TACTIQUE ACTIVÉ !</p>
                    <p>Je suis votre assistant intelligent pour les opérations de gestion des stages.</p>
                    <p><strong>CAPACITÉS OPÉRATIONNELLES :</strong></p>
                    <ul>
                        <li>🔍 <strong>RECONNAISSANCE</strong> - Recherche d'informations sur les stagiaires et missions</li>
                        <li>📊 <strong>INTELLIGENCE</strong> - Génération de rapports et analyses tactiques</li>
                        <li>📝 <strong>DOCUMENTATION</strong> - Rédaction de documents administratifs</li>
                        <li>⚡ <strong>AUTOMATISATION</strong> - Exécution de tâches répétitives</li>
                        <li>❓ <strong>SUPPORT</strong> - Assistance sur les procédures système</li>
                    </ul>
                    <p style="color: var(--accent-color); font-weight: 600;">PRÊT POUR MISSION. QUELLE EST VOTRE DEMANDE ?</p>
                </div>
                <div class="message-time">
                    <span style="font-family: 'Orbitron', monospace; color: var(--text-muted);">SYSTÈME ACTIVÉ</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Suggestions rapides -->
    <div class="quick-suggestions radar-scan" id="quickSuggestions" style="background: var(--bg-military-light); border-top: 2px solid var(--accent-color);">
        <div class="suggestions-header">
            <span style="color: var(--text-white); font-family: 'Orbitron', monospace; font-weight: 700;">COMMANDES RAPIDES :</span>
        </div>
        <div class="suggestions-list">
            <button class="suggestion-btn military-glow" data-text="Combien de stagiaires sont actuellement en stage ?" style="background: var(--bg-military); color: var(--text-white); border: 1px solid var(--accent-color);">
                📊 STATISTIQUES TACTIQUES
            </button>
            <button class="suggestion-btn military-glow" data-text="Génère une convention de stage pour le prochain stagiaire" style="background: var(--bg-military); color: var(--text-white); border: 1px solid var(--accent-color);">
                📝 GÉNÉRER DOCUMENTATION
            </button>
            <button class="suggestion-btn military-glow" data-text="Quels sont les stages qui se terminent cette semaine ?" style="background: var(--bg-military); color: var(--text-white); border: 1px solid var(--accent-color);">
                ⏰ ÉCHÉANCES MISSION
            </button>
            <button class="suggestion-btn military-glow" data-text="Comment ajouter un nouveau stagiaire ?" style="background: var(--bg-military); color: var(--text-white); border: 1px solid var(--accent-color);">
                ❓ AIDE SYSTÈME
            </button>
            <button class="suggestion-btn military-glow" data-text="Exporte la liste des stagiaires de la promotion 2024" style="background: var(--bg-military); color: var(--text-white); border: 1px solid var(--accent-color);">
                📋 EXPORT DONNÉES
            </button>
            <button class="suggestion-btn military-glow" data-text="Quelles sont les alertes en cours ?" style="background: var(--bg-military); color: var(--text-white); border: 1px solid var(--accent-color);">
                🚨 ALERTES SYSTÈME
            </button>
        </div>
    </div>

    <!-- Zone de saisie -->
    <div class="chat-input-area">
        <div class="input-container">
            <div class="input-actions">
                <button class="input-action-btn" id="attachFileBtn" title="Joindre un fichier">
                    <i class="fas fa-paperclip"></i>
                </button>
                <button class="input-action-btn" id="voiceInputBtn" title="Saisie vocale">
                    <i class="fas fa-microphone"></i>
                </button>
            </div>
            <div class="input-field">
                <textarea 
                    id="chatInput" 
                    placeholder="Tapez votre message ici... (Shift+Entrée pour nouvelle ligne)"
                    rows="1"
                ></textarea>
            </div>
            <div class="send-actions">
                <button class="send-btn" id="sendMessageBtn" disabled>
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
        
        <!-- Indicateur de frappe -->
        <div class="typing-indicator" id="typingIndicator" style="display: none;">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <span class="typing-text">L'assistant réfléchit...</span>
        </div>
    </div>
</div>

<!-- Panneau latéral d'aide -->
<div class="help-panel" id="helpPanel">
    <div class="help-header">
        <h4>Guide d'utilisation</h4>
        <button class="close-help" id="closeHelpPanel">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div class="help-content">
        <div class="help-section">
            <h5>🤖 Capacités de l'assistant</h5>
            <ul>
                <li>Recherche intelligente dans la base de données</li>
                <li>Génération automatique de documents</li>
                <li>Analyse de données et rapports</li>
                <li>Aide contextuelle sur l'application</li>
                <li>Automatisation de tâches</li>
            </ul>
        </div>
        
        <div class="help-section">
            <h5>💬 Exemples de commandes</h5>
            <div class="command-example">
                <code>"Trouve tous les stages qui se terminent ce mois"</code>
            </div>
            <div class="command-example">
                <code>"Génère un rapport sur la promotion 2024"</code>
            </div>
            <div class="command-example">
                <code>"Combien de stagiaires sont en informatique ?"</code>
            </div>
            <div class="command-example">
                <code>"Crée une attestation pour Jean Dupont"</code>
            </div>
        </div>
        
        <div class="help-section">
            <h5>⌨️ Raccourcis clavier</h5>
            <ul>
                <li><kbd>Entrée</kbd> - Envoyer le message</li>
                <li><kbd>Shift + Entrée</kbd> - Nouvelle ligne</li>
                <li><kbd>Ctrl + K</kbd> - Effacer la conversation</li>
                <li><kbd>Ctrl + /</kbd> - Afficher/masquer l'aide</li>
            </ul>
        </div>
    </div>
</div>

<!-- Modal Paramètres du chat -->
<div class="modal" id="chatSettingsModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Paramètres de l'assistant</h5>
                <button class="modal-close" id="closeChatSettingsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Mode de réponse</label>
                    <select class="form-select" id="responseMode">
                        <option value="detailed">Détaillé (recommandé)</option>
                        <option value="concise">Concis</option>
                        <option value="technical">Technique</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Langue</label>
                    <select class="form-select" id="chatLanguage">
                        <option value="fr">Français</option>
                        <option value="en">English</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="enableNotifications" checked>
                        <label class="form-check-label" for="enableNotifications">
                            Activer les notifications sonores
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="saveConversations" checked>
                        <label class="form-check-label" for="saveConversations">
                            Sauvegarder les conversations
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="enableSuggestions" checked>
                        <label class="form-check-label" for="enableSuggestions">
                            Afficher les suggestions rapides
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelChatSettings">Annuler</button>
                <button class="btn btn-primary" id="saveChatSettings">
                    <i class="fas fa-save"></i>
                    Enregistrer
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.chatbot-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - var(--topbar-height) - 4rem);
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
}

.chat-header-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.bot-avatar {
    width: 3rem;
    height: 3rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.bot-info h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.125rem;
    font-weight: 600;
}

.bot-status {
    margin: 0;
    font-size: 0.875rem;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background-color: #10b981;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chat-actions {
    display: flex;
    gap: 0.5rem;
}

.chat-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
}

.chat-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background-color: var(--bg-secondary);
}

.message {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.message.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, var(--success-color), #34d399);
    color: white;
}

.message-content {
    flex: 1;
    max-width: 70%;
}

.user-message .message-content {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.message-bubble {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
}

.user-message .message-bubble {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border: none;
}

.message-bubble p {
    margin: 0 0 0.5rem 0;
}

.message-bubble p:last-child {
    margin-bottom: 0;
}

.message-bubble ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.message-bubble li {
    margin-bottom: 0.25rem;
}

.message-time {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.user-message .message-time {
    text-align: right;
}

.quick-suggestions {
    padding: 1rem 1.5rem;
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-color);
}

.suggestions-header {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.75rem;
}

.suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.suggestion-btn {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.suggestion-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chat-input-area {
    padding: 1.5rem;
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-color);
}

.input-container {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 0.75rem;
}

.input-actions {
    display: flex;
    gap: 0.5rem;
}

.input-action-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.input-action-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.input-field {
    flex: 1;
}

.input-field textarea {
    width: 100%;
    border: none;
    background: none;
    resize: none;
    outline: none;
    font-family: inherit;
    font-size: 0.875rem;
    line-height: 1.5;
    max-height: 120px;
}

.send-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border: none;
    color: white;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.send-btn:hover:not(:disabled) {
    transform: scale(1.05);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.75rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
}

.typing-dots span {
    width: 0.375rem;
    height: 0.375rem;
    background-color: var(--primary-color);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.help-panel {
    position: fixed;
    top: var(--topbar-height);
    right: -400px;
    width: 400px;
    height: calc(100vh - var(--topbar-height));
    background-color: var(--bg-primary);
    border-left: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    transition: right var(--transition-normal);
    z-index: 1000;
    overflow-y: auto;
}

.help-panel.show {
    right: 0;
}

.help-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.help-header h4 {
    margin: 0;
    font-weight: 600;
}

.close-help {
    background: none;
    border: none;
    font-size: 1.125rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
}

.close-help:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.help-content {
    padding: 1.5rem;
}

.help-section {
    margin-bottom: 2rem;
}

.help-section h5 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.help-section ul {
    margin: 0;
    padding-left: 1.5rem;
}

.help-section li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.command-example {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

kbd {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    padding: 0.125rem 0.375rem;
    font-size: 0.75rem;
    font-family: monospace;
}

@media (max-width: 768px) {
    .chatbot-container {
        height: calc(100vh - var(--topbar-height) - 2rem);
    }
    
    .help-panel {
        width: 100%;
        right: -100%;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .suggestions-list {
        flex-direction: column;
    }
    
    .suggestion-btn {
        text-align: left;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/chatbot.js') }}"></script>
{% endblock %}
