{% extends "base.html" %}

{% block title %}Gestion des Documents - StageManager{% endblock %}
{% block page_title %}Gestion des Documents{% endblock %}

{% block content %}
<!-- Header avec actions -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-header-left">
            <h2 class="page-title">Documents</h2>
            <p class="page-description">Générez et gérez les documents administratifs</p>
        </div>
        <div class="page-header-right">
            <button class="btn btn-secondary" id="templateManagerBtn">
                <i class="fas fa-cog"></i>
                Gérer les modèles
            </button>
            <button class="btn btn-primary" id="generateDocBtn">
                <i class="fas fa-plus"></i>
                Générer un document
            </button>
        </div>
    </div>
</div>

<!-- Types de documents disponibles -->
<div class="document-types-section mb-4">
    <div class="row">
        <div class="col-3">
            <div class="document-type-card" data-type="convention">
                <div class="doc-icon">
                    <i class="fas fa-file-contract"></i>
                </div>
                <div class="doc-info">
                    <h5>Convention de stage</h5>
                    <p>Document officiel définissant les modalités du stage</p>
                </div>
                <button class="btn btn-primary btn-sm generate-btn">
                    <i class="fas fa-plus"></i>
                    Générer
                </button>
            </div>
        </div>
        
        <div class="col-3">
            <div class="document-type-card" data-type="attestation">
                <div class="doc-icon">
                    <i class="fas fa-certificate"></i>
                </div>
                <div class="doc-info">
                    <h5>Attestation de stage</h5>
                    <p>Certificat de fin de stage pour le stagiaire</p>
                </div>
                <button class="btn btn-primary btn-sm generate-btn">
                    <i class="fas fa-plus"></i>
                    Générer
                </button>
            </div>
        </div>
        
        <div class="col-3">
            <div class="document-type-card" data-type="rapport">
                <div class="doc-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="doc-info">
                    <h5>Rapport de stage</h5>
                    <p>Modèle de rapport d'activités du stagiaire</p>
                </div>
                <button class="btn btn-primary btn-sm generate-btn">
                    <i class="fas fa-plus"></i>
                    Générer
                </button>
            </div>
        </div>
        
        <div class="col-3">
            <div class="document-type-card" data-type="liste">
                <div class="doc-icon">
                    <i class="fas fa-list"></i>
                </div>
                <div class="doc-info">
                    <h5>Liste des stagiaires</h5>
                    <p>Export Excel/PDF des listes de stagiaires</p>
                </div>
                <button class="btn btn-primary btn-sm generate-btn">
                    <i class="fas fa-plus"></i>
                    Générer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filtres et recherche -->
<div class="filters-section">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-4">
                    <div class="form-group">
                        <label class="form-label">Rechercher</label>
                        <div class="search-input">
                            <input type="text" class="form-control" id="searchDocuments" placeholder="Nom du document, stagiaire...">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">Type</label>
                        <select class="form-select" id="filterTypeDoc">
                            <option value="">Tous</option>
                            <option value="convention">Convention</option>
                            <option value="attestation">Attestation</option>
                            <option value="rapport">Rapport</option>
                            <option value="liste">Liste</option>
                        </select>
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">Format</label>
                        <select class="form-select" id="filterFormat">
                            <option value="">Tous</option>
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="word">Word</option>
                        </select>
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">Date</label>
                        <input type="date" class="form-control" id="filterDate">
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-secondary w-100" id="resetDocFilters">
                            <i class="fas fa-undo"></i>
                            Réinitialiser
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Liste des documents générés -->
<div class="documents-section">
    <div class="card">
        <div class="card-header">
            <div class="card-header-content">
                <h5 class="card-title">Documents générés</h5>
                <div class="card-actions">
                    <button class="btn btn-sm btn-secondary" id="bulkDownloadBtn">
                        <i class="fas fa-download"></i>
                        Télécharger sélectionnés
                    </button>
                    <button class="btn btn-sm btn-danger" id="bulkDeleteBtn">
                        <i class="fas fa-trash"></i>
                        Supprimer sélectionnés
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-container">
                <table class="table" id="documentsTable">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAllDocs">
                            </th>
                            <th>Nom du document</th>
                            <th>Type</th>
                            <th>Format</th>
                            <th>Stagiaire/Promotion</th>
                            <th>Taille</th>
                            <th>Date de création</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="documentsTableBody">
                        <!-- Les données seront chargées dynamiquement -->
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="pagination-section">
                <div class="pagination-info">
                    <span id="docPaginationInfo">Affichage de 1 à 10 sur 0 documents</span>
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-sm btn-secondary" id="docPrevPage" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="pagination-pages" id="docPaginationPages">
                        <!-- Les numéros de page seront générés dynamiquement -->
                    </div>
                    <button class="btn btn-sm btn-secondary" id="docNextPage" disabled>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Génération de document -->
<div class="modal" id="generateDocModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="generateDocModalTitle">Générer un document</h5>
                <button class="modal-close" id="closeGenerateDocModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="generateDocForm">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Type de document *</label>
                                <select class="form-select" id="docType" required>
                                    <option value="">Sélectionner un type</option>
                                    <option value="convention">Convention de stage</option>
                                    <option value="attestation">Attestation de stage</option>
                                    <option value="rapport">Modèle de rapport</option>
                                    <option value="liste">Liste des stagiaires</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Format *</label>
                                <select class="form-select" id="docFormat" required>
                                    <option value="">Sélectionner un format</option>
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                    <option value="word">Word</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group" id="stagiaireSelectGroup">
                        <label class="form-label">Stagiaire</label>
                        <select class="form-select" id="docStagiaire">
                            <option value="">Sélectionner un stagiaire</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="promotionSelectGroup">
                        <label class="form-label">Promotion</label>
                        <select class="form-select" id="docPromotion">
                            <option value="">Sélectionner une promotion</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="stageSelectGroup">
                        <label class="form-label">Stage</label>
                        <select class="form-select" id="docStage">
                            <option value="">Sélectionner un stage</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Nom du fichier</label>
                        <input type="text" class="form-control" id="docFileName" placeholder="Nom automatique si vide">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Options avancées</label>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="includeSignature">
                            <label class="form-check-label" for="includeSignature">
                                Inclure les zones de signature
                            </label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="includeWatermark">
                            <label class="form-check-label" for="includeWatermark">
                                Ajouter un filigrane
                            </label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="autoSend">
                            <label class="form-check-label" for="autoSend">
                                Envoyer automatiquement par email
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelGenerateDoc">Annuler</button>
                <button class="btn btn-primary" id="generateDocument">
                    <i class="fas fa-file-export"></i>
                    Générer le document
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Aperçu document -->
<div class="modal" id="previewDocModal">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aperçu du document</h5>
                <button class="modal-close" id="closePreviewModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="document-preview" id="documentPreview">
                    <!-- L'aperçu sera chargé ici -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="closePreview">Fermer</button>
                <button class="btn btn-primary" id="downloadPreviewDoc">
                    <i class="fas fa-download"></i>
                    Télécharger
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.document-types-section {
    margin-bottom: 2rem;
}

.document-type-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    transition: all var(--transition-fast);
    cursor: pointer;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.document-type-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    border-color: var(--primary-color);
}

.doc-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.doc-info {
    flex: 1;
    margin-bottom: 1rem;
}

.doc-info h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.doc-info p {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin: 0;
}

.generate-btn {
    margin-top: auto;
}

.document-preview {
    min-height: 500px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-placeholder {
    color: var(--text-muted);
    font-size: 1.125rem;
}

.preview-iframe {
    width: 100%;
    height: 600px;
    border: none;
    border-radius: var(--radius-md);
}

.document-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
}

.document-status.generated {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.document-status.processing {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.document-status.error {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.file-size {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.form-check {
    margin-bottom: 0.5rem;
}

.form-check-input {
    margin-right: 0.5rem;
}

.bulk-actions {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .document-types-section .row {
        flex-direction: column;
    }
    
    .document-type-card {
        margin-bottom: 1rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/documents.js') }}"></script>
{% endblock %}
