/* Cartes et Composants Militaires */

/* Cartes de base avec style militaire */
.card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
    border: 1px solid var(--border-military);
    border-radius: var(--radius-military);
    box-shadow: var(--shadow-military);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-color), var(--accent-secondary));
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.card:hover::before {
    opacity: 1;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-military), var(--shadow-glow);
}

/* Cartes avec effet militaire */
.card.military-glow {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.98), 
        rgba(248, 250, 252, 0.95));
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
}

.card.military-glow::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--accent-color), var(--accent-secondary), var(--primary-color));
    border-radius: inherit;
    z-index: -1;
    animation: border-glow 3s infinite;
}

/* En-têtes de cartes militaires */
.card-header {
    background: var(--bg-military);
    color: var(--text-white);
    border-bottom: 2px solid var(--accent-color);
    padding: 1rem 1.5rem;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-title {
    margin: 0;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-title i {
    color: var(--accent-color);
    text-shadow: 0 0 10px var(--accent-color);
}

/* Boutons militaires */
.btn {
    border-radius: var(--radius-military);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--bg-military);
    border: 2px solid var(--accent-color);
    color: var(--text-white);
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.3);
}

.btn-primary:hover {
    background: var(--bg-military-light);
    border-color: var(--accent-secondary);
    box-shadow: 0 0 20px rgba(231, 76, 60, 0.5);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, var(--operational-color), #16a34a);
    border: 2px solid var(--operational-color);
    color: var(--text-white);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--accent-secondary));
    border: 2px solid var(--warning-color);
    color: var(--text-white);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    border: 2px solid var(--danger-color);
    color: var(--text-white);
}

/* Icônes de statistiques militaires */
.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    position: relative;
    box-shadow: var(--shadow-md);
    animation: pulse-military var(--pulse-duration) infinite;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--accent-color), var(--accent-secondary));
    border-radius: inherit;
    z-index: -1;
    animation: glow-border 2s infinite;
}

/* Badges militaires */
.badge {
    background: var(--bg-military);
    color: var(--text-white);
    border: 1px solid var(--accent-color);
    border-radius: var(--radius-military);
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.3);
}

.badge-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    animation: pulse-military 1s infinite;
}

.badge-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--accent-secondary));
}

.badge-success {
    background: linear-gradient(135deg, var(--operational-color), #16a34a);
}

/* Alertes militaires */
.alert {
    border: 2px solid;
    border-radius: var(--radius-military);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--accent-color), var(--accent-secondary));
    animation: pulse-glow var(--pulse-duration) infinite;
}

.alert-danger {
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-warning {
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.alert-success {
    border-color: var(--operational-color);
    color: var(--operational-color);
}

.alert-info {
    border-color: var(--info-color);
    color: var(--info-color);
}

/* Formulaires militaires */
.form-control, .form-select {
    border: 2px solid var(--border-military);
    border-radius: var(--radius-military);
    background: rgba(255, 255, 255, 0.95);
    transition: all var(--transition-normal);
    font-family: 'Inter', sans-serif;
}

.form-control:focus, .form-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
    background: rgba(255, 255, 255, 1);
}

/* Tables militaires */
.table {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--radius-military);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table thead th {
    background: var(--bg-military);
    color: var(--text-white);
    border: none;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1rem;
}

.table tbody tr {
    transition: all var(--transition-fast);
}

.table tbody tr:hover {
    background: rgba(231, 76, 60, 0.1);
    transform: scale(1.01);
}

/* Modales militaires */
.modal-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    border: 2px solid var(--accent-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-military);
}

.modal-header {
    background: var(--bg-military);
    color: var(--text-white);
    border-bottom: 2px solid var(--accent-color);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.modal-title {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Pagination militaire */
.pagination .page-link {
    background: var(--bg-military);
    border: 1px solid var(--accent-color);
    color: var(--text-white);
    font-weight: 600;
    transition: all var(--transition-normal);
}

.pagination .page-link:hover {
    background: var(--bg-military-light);
    border-color: var(--accent-secondary);
    color: var(--text-white);
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-secondary));
    border-color: var(--accent-color);
    box-shadow: 0 0 15px rgba(231, 76, 60, 0.5);
}

/* Tooltips militaires */
.tooltip .tooltip-inner {
    background: var(--bg-military);
    color: var(--text-white);
    border: 1px solid var(--accent-color);
    border-radius: var(--radius-military);
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-md);
}

/* Responsive */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .stat-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
    
    .card-title {
        font-size: 0.9rem;
    }
}
