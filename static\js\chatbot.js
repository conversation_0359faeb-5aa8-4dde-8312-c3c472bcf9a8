// Gestionnaire du Chatbot IA
class ChatbotManager {
    constructor() {
        this.messages = [];
        this.isTyping = false;
        this.settings = {
            responseMode: 'detailed',
            language: 'fr',
            enableNotifications: true,
            saveConversations: true,
            enableSuggestions: true
        };
        
        this.init();
        this.bindEvents();
        this.loadSettings();
    }

    init() {
        // Éléments DOM
        this.chatMessages = document.getElementById('chatMessages');
        this.chatInput = document.getElementById('chatInput');
        this.sendBtn = document.getElementById('sendMessageBtn');
        this.typingIndicator = document.getElementById('typingIndicator');
        this.quickSuggestions = document.getElementById('quickSuggestions');
        this.helpPanel = document.getElementById('helpPanel');
        this.settingsModal = document.getElementById('chatSettingsModal');
        
        // Auto-resize du textarea
        this.setupAutoResize();
    }

    bindEvents() {
        // Envoi de message
        this.sendBtn?.addEventListener('click', () => this.sendMessage());
        
        // Gestion du textarea
        this.chatInput?.addEventListener('input', (e) => {
            this.handleInputChange(e);
            this.updateSendButton();
        });
        
        this.chatInput?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Suggestions rapides
        document.querySelectorAll('.suggestion-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const text = e.target.getAttribute('data-text');
                this.chatInput.value = text;
                this.updateSendButton();
                this.sendMessage();
            });
        });
        
        // Actions du header
        document.getElementById('clearChatBtn')?.addEventListener('click', () => this.clearChat());
        document.getElementById('exportChatBtn')?.addEventListener('click', () => this.exportChat());
        document.getElementById('chatSettingsBtn')?.addEventListener('click', () => this.openSettings());
        
        // Panneau d'aide
        document.getElementById('closeHelpPanel')?.addEventListener('click', () => this.closeHelp());
        
        // Modal paramètres
        document.getElementById('closeChatSettingsModal')?.addEventListener('click', () => this.closeSettings());
        document.getElementById('cancelChatSettings')?.addEventListener('click', () => this.closeSettings());
        document.getElementById('saveChatSettings')?.addEventListener('click', () => this.saveSettings());
        
        // Actions d'entrée
        document.getElementById('attachFileBtn')?.addEventListener('click', () => this.attachFile());
        document.getElementById('voiceInputBtn')?.addEventListener('click', () => this.toggleVoiceInput());
        
        // Raccourcis clavier
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.clearChat();
            }
            if (e.ctrlKey && e.key === '/') {
                e.preventDefault();
                this.toggleHelp();
            }
        });
        
        // Fermer modals en cliquant à l'extérieur
        this.settingsModal?.addEventListener('click', (e) => {
            if (e.target === this.settingsModal) this.closeSettings();
        });
    }

    setupAutoResize() {
        if (!this.chatInput) return;
        
        this.chatInput.addEventListener('input', () => {
            this.chatInput.style.height = 'auto';
            this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
        });
    }

    handleInputChange(e) {
        const value = e.target.value.trim();
        this.updateSendButton();
        
        // Masquer les suggestions si l'utilisateur tape
        if (value && this.settings.enableSuggestions) {
            this.quickSuggestions.style.display = 'none';
        } else if (!value && this.settings.enableSuggestions) {
            this.quickSuggestions.style.display = 'block';
        }
    }

    updateSendButton() {
        const hasText = this.chatInput.value.trim().length > 0;
        this.sendBtn.disabled = !hasText || this.isTyping;
    }

    async sendMessage() {
        const text = this.chatInput.value.trim();
        if (!text || this.isTyping) return;
        
        // Ajouter le message utilisateur
        this.addMessage(text, 'user');
        
        // Vider l'input
        this.chatInput.value = '';
        this.chatInput.style.height = 'auto';
        this.updateSendButton();
        
        // Masquer les suggestions
        this.quickSuggestions.style.display = 'none';
        
        // Afficher l'indicateur de frappe
        this.showTyping();
        
        try {
            // Simuler une réponse de l'IA (à remplacer par un vrai appel API)
            const response = await this.getAIResponse(text);
            
            // Masquer l'indicateur de frappe
            this.hideTyping();
            
            // Ajouter la réponse de l'IA
            this.addMessage(response, 'bot');
            
            // Jouer un son de notification si activé
            if (this.settings.enableNotifications) {
                this.playNotificationSound();
            }
            
        } catch (error) {
            this.hideTyping();
            this.addMessage('Désolé, je rencontre un problème technique. Veuillez réessayer.', 'bot');
            console.error('Erreur chatbot:', error);
        }
    }

    addMessage(text, sender) {
        const message = {
            id: Date.now(),
            text: text,
            sender: sender,
            timestamp: new Date()
        };
        
        this.messages.push(message);
        this.renderMessage(message);
        this.scrollToBottom();
        
        // Sauvegarder si activé
        if (this.settings.saveConversations) {
            this.saveConversation();
        }
    }

    renderMessage(message) {
        const messageEl = document.createElement('div');
        messageEl.className = `message ${message.sender}-message`;
        messageEl.setAttribute('data-message-id', message.id);
        
        const avatar = message.sender === 'bot' ? 
            '<i class="fas fa-robot"></i>' : 
            '<i class="fas fa-user"></i>';
        
        messageEl.innerHTML = `
            <div class="message-avatar">
                ${avatar}
            </div>
            <div class="message-content">
                <div class="message-bubble">
                    ${this.formatMessageText(message.text)}
                </div>
                <div class="message-time">
                    <span>${this.formatTime(message.timestamp)}</span>
                </div>
            </div>
        `;
        
        this.chatMessages.appendChild(messageEl);
    }

    formatMessageText(text) {
        // Convertir les retours à la ligne en <br>
        text = text.replace(/\n/g, '<br>');
        
        // Détecter et formater les listes
        text = text.replace(/^- (.+)$/gm, '<li>$1</li>');
        text = text.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
        
        // Détecter les liens
        text = text.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
        
        // Détecter le code
        text = text.replace(/`([^`]+)`/g, '<code>$1</code>');
        
        return text;
    }

    formatTime(date) {
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return 'Maintenant';
        if (diff < 3600000) return `Il y a ${Math.floor(diff / 60000)} min`;
        if (diff < 86400000) return `Il y a ${Math.floor(diff / 3600000)}h`;
        
        return date.toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    async getAIResponse(userMessage) {
        // Simuler un délai de réponse
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
        
        // Réponses simulées basées sur des mots-clés
        const lowerMessage = userMessage.toLowerCase();
        
        if (lowerMessage.includes('statistique') || lowerMessage.includes('combien')) {
            return this.getStatsResponse();
        }
        
        if (lowerMessage.includes('convention') || lowerMessage.includes('génère') || lowerMessage.includes('document')) {
            return this.getDocumentResponse();
        }
        
        if (lowerMessage.includes('échéance') || lowerMessage.includes('termine') || lowerMessage.includes('alerte')) {
            return this.getAlertsResponse();
        }
        
        if (lowerMessage.includes('aide') || lowerMessage.includes('comment') || lowerMessage.includes('ajouter')) {
            return this.getHelpResponse();
        }
        
        if (lowerMessage.includes('export') || lowerMessage.includes('liste')) {
            return this.getExportResponse();
        }
        
        // Réponse par défaut
        return this.getDefaultResponse();
    }

    getStatsResponse() {
        return `📊 **Statistiques actuelles :**

• **Stagiaires actifs :** 45
• **Stages en cours :** 38
• **Promotions actives :** 3
• **Documents générés ce mois :** 127

**Répartition par type de stage :**
- Stage d'observation : 15 (33%)
- Stage de formation : 20 (44%)
- Stage de perfectionnement : 10 (23%)

Souhaitez-vous des détails sur une catégorie spécifique ?`;
    }

    getDocumentResponse() {
        return `📝 **Génération de documents :**

Je peux vous aider à générer plusieurs types de documents :

• **Convention de stage** - Document officiel
• **Attestation de stage** - Certificat de fin de stage
• **Rapport de stage** - Modèle pour le stagiaire
• **Liste des stagiaires** - Export Excel/PDF

Pour générer un document, précisez :
1. Le type de document souhaité
2. Le stagiaire concerné (si applicable)
3. Le format (PDF, Word, Excel)

Exemple : "Génère une convention PDF pour Marie Dubois"`;
    }

    getAlertsResponse() {
        return `🚨 **Alertes et échéances :**

**Échéances cette semaine :**
• Pierre Durand - Stage se termine le 21/06
• Sophie Bernard - Examen prévu le 25/06
• Luc Moreau - Documents manquants (échéance 30/06)

**Actions recommandées :**
1. Contacter Pierre Durand pour la prolongation
2. Rappeler l'examen à Sophie Bernard
3. Relancer Luc Moreau pour les documents

Voulez-vous que je génère des rappels automatiques ?`;
    }

    getHelpResponse() {
        return `❓ **Aide sur l'application :**

**Pour ajouter un nouveau stagiaire :**
1. Allez dans "Stagiaires" → "Nouveau stagiaire"
2. Remplissez les informations obligatoires (nom, prénom, email)
3. Ajoutez les détails complémentaires
4. Cliquez sur "Enregistrer"

**Raccourcis utiles :**
• Ctrl+K : Effacer cette conversation
• Ctrl+/ : Afficher/masquer l'aide
• Entrée : Envoyer un message
• Shift+Entrée : Nouvelle ligne

Besoin d'aide sur une fonctionnalité spécifique ?`;
    }

    getExportResponse() {
        return `📋 **Export de données :**

**Formats disponibles :**
• **PDF** - Pour les documents officiels
• **Excel** - Pour les analyses et listes
• **CSV** - Pour l'import dans d'autres systèmes

**Types d'exports :**
• Liste des stagiaires par promotion
• Rapport de stages par période
• Statistiques détaillées
• Planning des stages

Précisez le type d'export et la période souhaitée.
Exemple : "Exporte la liste Excel des stagiaires 2024"`;
    }

    getDefaultResponse() {
        return `🤖 Je suis là pour vous aider avec la gestion des stages !

**Je peux vous assister pour :**
• Rechercher des informations
• Générer des documents
• Analyser des données
• Automatiser des tâches
• Répondre à vos questions

Posez-moi une question spécifique ou utilisez les suggestions rapides ci-dessous.`;
    }

    showTyping() {
        this.isTyping = true;
        this.typingIndicator.style.display = 'flex';
        this.updateSendButton();
        this.scrollToBottom();
    }

    hideTyping() {
        this.isTyping = false;
        this.typingIndicator.style.display = 'none';
        this.updateSendButton();
        
        // Réafficher les suggestions si l'input est vide
        if (!this.chatInput.value.trim() && this.settings.enableSuggestions) {
            this.quickSuggestions.style.display = 'block';
        }
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    clearChat() {
        if (confirm('Êtes-vous sûr de vouloir effacer toute la conversation ?')) {
            this.messages = [];
            this.chatMessages.innerHTML = '';
            this.quickSuggestions.style.display = 'block';
            
            // Ajouter le message de bienvenue
            setTimeout(() => {
                this.addWelcomeMessage();
            }, 500);
        }
    }

    addWelcomeMessage() {
        const welcomeMessage = `👋 Bonjour ! Je suis votre assistant intelligent pour la gestion des stages.

Je peux vous aider avec :
• 🔍 Rechercher des informations sur les stagiaires et stages
• 📊 Générer des rapports et statistiques
• 📝 Rédiger des documents administratifs
• ⚡ Automatiser des tâches répétitives
• ❓ Répondre à vos questions sur l'application

Comment puis-je vous aider aujourd'hui ?`;
        
        this.addMessage(welcomeMessage, 'bot');
    }

    exportChat() {
        const chatData = {
            messages: this.messages,
            exportDate: new Date().toISOString(),
            settings: this.settings
        };
        
        const blob = new Blob([JSON.stringify(chatData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `conversation-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        
        window.stageManager.showToast('Conversation exportée avec succès', 'success');
    }

    openSettings() {
        this.fillSettingsForm();
        this.settingsModal.classList.add('show');
    }

    closeSettings() {
        this.settingsModal.classList.remove('show');
    }

    fillSettingsForm() {
        document.getElementById('responseMode').value = this.settings.responseMode;
        document.getElementById('chatLanguage').value = this.settings.language;
        document.getElementById('enableNotifications').checked = this.settings.enableNotifications;
        document.getElementById('saveConversations').checked = this.settings.saveConversations;
        document.getElementById('enableSuggestions').checked = this.settings.enableSuggestions;
    }

    saveSettings() {
        this.settings = {
            responseMode: document.getElementById('responseMode').value,
            language: document.getElementById('chatLanguage').value,
            enableNotifications: document.getElementById('enableNotifications').checked,
            saveConversations: document.getElementById('saveConversations').checked,
            enableSuggestions: document.getElementById('enableSuggestions').checked
        };
        
        localStorage.setItem('chatbot_settings', JSON.stringify(this.settings));
        
        // Appliquer les paramètres
        this.applySettings();
        
        this.closeSettings();
        window.stageManager.showToast('Paramètres sauvegardés', 'success');
    }

    loadSettings() {
        const saved = localStorage.getItem('chatbot_settings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
        this.applySettings();
    }

    applySettings() {
        // Afficher/masquer les suggestions
        this.quickSuggestions.style.display = 
            this.settings.enableSuggestions ? 'block' : 'none';
    }

    saveConversation() {
        if (this.settings.saveConversations) {
            localStorage.setItem('chatbot_conversation', JSON.stringify(this.messages));
        }
    }

    toggleHelp() {
        this.helpPanel.classList.toggle('show');
    }

    closeHelp() {
        this.helpPanel.classList.remove('show');
    }

    attachFile() {
        window.stageManager.showToast('Fonctionnalité de pièce jointe en cours de développement', 'info');
    }

    toggleVoiceInput() {
        window.stageManager.showToast('Saisie vocale en cours de développement', 'info');
    }

    playNotificationSound() {
        // Créer un son de notification simple
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = 800;
        oscillator.type = 'sine';
        
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    }
}

// Initialiser le chatbot
document.addEventListener('DOMContentLoaded', () => {
    window.chatbotManager = new ChatbotManager();
});
