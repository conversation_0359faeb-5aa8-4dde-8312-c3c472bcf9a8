from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import json
import os
from datetime import datetime, timedelta
import uuid
from werkzeug.utils import secure_filename
import io
import base64

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['UPLOAD_FOLDER'] = 'uploads'
CORS(app)

# Créer le dossier uploads s'il n'existe pas
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Données en mémoire (simulation de base de données)
data_store = {
    'stagiaires': [
        {
            'id': 'stg-001',
            'nom': 'Dupont',
            'prenom': 'Jean',
            'email': '<EMAIL>',
            'telephone': '0123456789',
            'date_naissance': '1995-03-15',
            'adresse': '123 Rue de la Paix, 75001 Paris',
            'niveau_etude': 'bac+3',
            'specialite': 'Informatique',
            'photo': '',
            'date_inscription': '2024-01-15T10:30:00',
            'statut': 'actif'
        },
        {
            'id': 'stg-002',
            'nom': '<PERSON>',
            'prenom': 'Sophie',
            'email': '<EMAIL>',
            'telephone': '0123456790',
            'date_naissance': '1996-07-22',
            'adresse': '456 Avenue des Champs, 69000 Lyon',
            'niveau_etude': 'bac+2',
            'specialite': 'Gestion',
            'photo': '',
            'date_inscription': '2024-02-01T14:15:00',
            'statut': 'actif'
        },
        {
            'id': 'stg-003',
            'nom': 'Bernard',
            'prenom': 'Pierre',
            'email': '<EMAIL>',
            'telephone': '0123456791',
            'date_naissance': '1994-11-08',
            'adresse': '789 Boulevard du Centre, 13000 Marseille',
            'niveau_etude': 'bac+5',
            'specialite': 'Ingénierie',
            'photo': '',
            'date_inscription': '2024-01-20T09:45:00',
            'statut': 'actif'
        }
    ],
    'stages': [
        {
            'id': 'stage-001',
            'stagiaire_id': 'stg-001',
            'type_stage_id': 1,
            'promotion_id': 'promo-001',
            'date_debut': '2024-03-01',
            'date_fin': '2024-08-31',
            'statut': 'en_cours',
            'unite_affectation': 'Service Informatique',
            'responsable': 'M. Durand',
            'objectifs': 'Développement d\'applications web et maintenance système',
            'date_creation': '2024-02-15T10:00:00'
        },
        {
            'id': 'stage-002',
            'stagiaire_id': 'stg-002',
            'type_stage_id': 2,
            'promotion_id': 'promo-001',
            'date_debut': '2024-04-01',
            'date_fin': '2024-09-30',
            'statut': 'en_cours',
            'unite_affectation': 'Service RH',
            'responsable': 'Mme Leblanc',
            'objectifs': 'Gestion administrative et recrutement',
            'date_creation': '2024-03-01T11:30:00'
        }
    ],
    'promotions': [
        {
            'id': 'promo-001',
            'annee': 2024,
            'filiere': 'Informatique et Gestion',
            'nom': 'Promotion 2024-A',
            'date_debut': '2024-03-01',
            'date_fin': '2024-12-31',
            'effectif_max': 50,
            'statut': 'active',
            'description': 'Promotion mixte informatique et gestion pour l\'année 2024',
            'date_creation': '2024-01-10T08:00:00'
        },
        {
            'id': 'promo-002',
            'annee': 2024,
            'filiere': 'Ingénierie',
            'nom': 'Promotion 2024-B',
            'date_debut': '2024-09-01',
            'date_fin': '2025-06-30',
            'effectif_max': 30,
            'statut': 'planifiee',
            'description': 'Promotion spécialisée en ingénierie pour 2024-2025',
            'date_creation': '2024-01-15T09:00:00'
        }
    ],
    'types_stages': [
        {
            'id': 1,
            'libelle': 'Stage d\'observation',
            'conditions_admission': 'Niveau Bac minimum, âge entre 18-25 ans'
        },
        {
            'id': 2,
            'libelle': 'Stage de formation',
            'conditions_admission': 'Diplôme universitaire, expérience préalable souhaitée'
        },
        {
            'id': 3,
            'libelle': 'Stage de perfectionnement',
            'conditions_admission': 'Expérience professionnelle de 2 ans minimum'
        }
    ],
    'documents': [
        {
            'id': 'doc-001',
            'nom': 'Convention_Jean_Dupont_2024.pdf',
            'type': 'convention',
            'format': 'pdf',
            'stagiaire_id': 'stg-001',
            'taille': '245 KB',
            'date_creation': '2024-02-20T15:30:00',
            'statut': 'generated',
            'chemin_fichier': '/documents/conventions/Convention_Jean_Dupont_2024.pdf'
        },
        {
            'id': 'doc-002',
            'nom': 'Liste_Stagiaires_Promo_2024A.xlsx',
            'type': 'liste',
            'format': 'excel',
            'promotion_id': 'promo-001',
            'taille': '89 KB',
            'date_creation': '2024-03-01T10:15:00',
            'statut': 'generated',
            'chemin_fichier': '/documents/listes/Liste_Stagiaires_Promo_2024A.xlsx'
        }
    ],
    'notifications': [
        {
            'id': 'notif-001',
            'contenu': 'Stage de Jean Dupont expire dans 3 jours',
            'type': 'warning',
            'date_envoi': '2024-06-16T09:00:00',
            'stagiaire_id': 'stg-001',
            'lu': False
        },
        {
            'id': 'notif-002',
            'contenu': 'Nouveau stagiaire inscrit: Sophie Martin',
            'type': 'info',
            'date_envoi': '2024-02-01T14:20:00',
            'stagiaire_id': 'stg-002',
            'lu': True
        }
    ],
    'utilisateurs': [
        {
            'id': 1,
            'nom_user': 'Admin',
            'email': '<EMAIL>',
            'role': 'admin'
        }
    ],
    'actions': []
}

# Routes principales
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html')

@app.route('/stagiaires')
def stagiaires():
    return render_template('stagiaires.html')

@app.route('/stages')
def stages():
    return render_template('stages.html')

@app.route('/promotions')
def promotions():
    return render_template('promotions.html')

@app.route('/documents')
def documents():
    return render_template('documents.html')

@app.route('/chatbot')
def chatbot():
    return render_template('chatbot.html')

# API Routes
@app.route('/api/stagiaires', methods=['GET', 'POST'])
def api_stagiaires():
    if request.method == 'GET':
        return jsonify(data_store['stagiaires'])
    
    elif request.method == 'POST':
        data = request.get_json()
        stagiaire = {
            'id': str(uuid.uuid4()),
            'nom': data.get('nom'),
            'prenom': data.get('prenom'),
            'email': data.get('email'),
            'telephone': data.get('telephone'),
            'date_naissance': data.get('date_naissance'),
            'adresse': data.get('adresse'),
            'niveau_etude': data.get('niveau_etude'),
            'specialite': data.get('specialite'),
            'photo': data.get('photo', ''),
            'date_inscription': datetime.now().isoformat(),
            'statut': 'actif'
        }
        data_store['stagiaires'].append(stagiaire)
        
        # Log action
        log_action('creation_stagiaire', f"Création du stagiaire {stagiaire['nom']} {stagiaire['prenom']}")
        
        return jsonify(stagiaire), 201

@app.route('/api/stagiaires/<stagiaire_id>', methods=['GET', 'PUT', 'DELETE'])
def api_stagiaire_detail(stagiaire_id):
    stagiaire = next((s for s in data_store['stagiaires'] if s['id'] == stagiaire_id), None)
    
    if not stagiaire:
        return jsonify({'error': 'Stagiaire non trouvé'}), 404
    
    if request.method == 'GET':
        return jsonify(stagiaire)
    
    elif request.method == 'PUT':
        data = request.get_json()
        stagiaire.update(data)
        log_action('modification_stagiaire', f"Modification du stagiaire {stagiaire['nom']} {stagiaire['prenom']}")
        return jsonify(stagiaire)
    
    elif request.method == 'DELETE':
        data_store['stagiaires'].remove(stagiaire)
        log_action('suppression_stagiaire', f"Suppression du stagiaire {stagiaire['nom']} {stagiaire['prenom']}")
        return jsonify({'message': 'Stagiaire supprimé'}), 200

@app.route('/api/stages', methods=['GET', 'POST'])
def api_stages():
    if request.method == 'GET':
        return jsonify(data_store['stages'])
    
    elif request.method == 'POST':
        data = request.get_json()
        stage = {
            'id': str(uuid.uuid4()),
            'stagiaire_id': data.get('stagiaire_id'),
            'type_stage_id': data.get('type_stage_id'),
            'promotion_id': data.get('promotion_id'),
            'date_debut': data.get('date_debut'),
            'date_fin': data.get('date_fin'),
            'statut': data.get('statut', 'en_cours'),
            'unite_affectation': data.get('unite_affectation'),
            'responsable': data.get('responsable'),
            'objectifs': data.get('objectifs'),
            'date_creation': datetime.now().isoformat()
        }
        data_store['stages'].append(stage)
        log_action('creation_stage', f"Création d'un nouveau stage")
        return jsonify(stage), 201

@app.route('/api/promotions', methods=['GET', 'POST'])
def api_promotions():
    if request.method == 'GET':
        return jsonify(data_store['promotions'])
    
    elif request.method == 'POST':
        data = request.get_json()
        promotion = {
            'id': str(uuid.uuid4()),
            'annee': data.get('annee'),
            'filiere': data.get('filiere'),
            'nom': data.get('nom'),
            'date_debut': data.get('date_debut'),
            'date_fin': data.get('date_fin'),
            'effectif_max': data.get('effectif_max', 50),
            'statut': data.get('statut', 'active'),
            'date_creation': datetime.now().isoformat()
        }
        data_store['promotions'].append(promotion)
        log_action('creation_promotion', f"Création de la promotion {promotion['nom']}")
        return jsonify(promotion), 201

@app.route('/api/types-stages')
def api_types_stages():
    return jsonify(data_store['types_stages'])

@app.route('/api/notifications')
def api_notifications():
    return jsonify(data_store['notifications'])

@app.route('/api/search')
def api_search():
    query = request.args.get('q', '').lower()
    results = {
        'stagiaires': [],
        'stages': [],
        'promotions': []
    }
    
    if query:
        # Recherche dans les stagiaires
        for stagiaire in data_store['stagiaires']:
            if (query in stagiaire.get('nom', '').lower() or 
                query in stagiaire.get('prenom', '').lower() or
                query in stagiaire.get('email', '').lower()):
                results['stagiaires'].append(stagiaire)
        
        # Recherche dans les stages
        for stage in data_store['stages']:
            if (query in stage.get('unite_affectation', '').lower() or
                query in stage.get('responsable', '').lower()):
                results['stages'].append(stage)
        
        # Recherche dans les promotions
        for promotion in data_store['promotions']:
            if (query in promotion.get('nom', '').lower() or
                query in promotion.get('filiere', '').lower()):
                results['promotions'].append(promotion)
    
    return jsonify(results)

@app.route('/api/stages/<stage_id>', methods=['GET', 'PUT', 'DELETE'])
def api_stage_detail(stage_id):
    stage = next((s for s in data_store['stages'] if s['id'] == stage_id), None)

    if not stage:
        return jsonify({'error': 'Stage non trouvé'}), 404

    if request.method == 'GET':
        return jsonify(stage)

    elif request.method == 'PUT':
        data = request.get_json()
        stage.update(data)
        log_action('modification_stage', f"Modification du stage {stage_id}")
        return jsonify(stage)

    elif request.method == 'DELETE':
        data_store['stages'].remove(stage)
        log_action('suppression_stage', f"Suppression du stage {stage_id}")
        return jsonify({'message': 'Stage supprimé'}), 200

@app.route('/api/promotions/<promotion_id>', methods=['GET', 'PUT', 'DELETE'])
def api_promotion_detail(promotion_id):
    promotion = next((p for p in data_store['promotions'] if p['id'] == promotion_id), None)

    if not promotion:
        return jsonify({'error': 'Promotion non trouvée'}), 404

    if request.method == 'GET':
        return jsonify(promotion)

    elif request.method == 'PUT':
        data = request.get_json()
        promotion.update(data)
        log_action('modification_promotion', f"Modification de la promotion {promotion['nom']}")
        return jsonify(promotion)

    elif request.method == 'DELETE':
        data_store['promotions'].remove(promotion)
        log_action('suppression_promotion', f"Suppression de la promotion {promotion['nom']}")
        return jsonify({'message': 'Promotion supprimée'}), 200

@app.route('/api/stats')
def api_stats():
    stats = {
        'total_stagiaires': len(data_store['stagiaires']),
        'total_stages': len(data_store['stages']),
        'total_promotions': len(data_store['promotions']),
        'stages_actifs': len([s for s in data_store['stages'] if s.get('statut') == 'en_cours']),
        'stagiaires_actifs': len([s for s in data_store['stagiaires'] if s.get('statut') == 'actif'])
    }
    return jsonify(stats)

def log_action(type_action, description):
    action = {
        'id': str(uuid.uuid4()),
        'type_action': type_action,
        'description': description,
        'date_action': datetime.now().isoformat(),
        'utilisateur': 'Admin'  # À adapter selon l'authentification
    }
    data_store['actions'].append(action)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
