from flask import Flask, render_template, request, jsonify, send_file, make_response
from flask_cors import CORS
import json
import os
from datetime import datetime, timedelta
import uuid
from werkzeug.utils import secure_filename
import io
import base64
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
import zipfile
import tempfile

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['UPLOAD_FOLDER'] = 'uploads'
CORS(app)

# Créer le dossier uploads s'il n'existe pas
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Données en mémoire (simulation de base de données)
data_store = {
    'stagiaires': [
        {
            'id': 'stg-001',
            'nom': 'Al-Idrissi',
            'prenom': 'Mohammed',
            'email': '<EMAIL>',
            'telephone': '0661234567',
            'date_naissance': '1995-03-15',
            'adresse': 'Hay Riad, Rabat 10000',
            'niveau_etude': 'bac+3',
            'specialite': 'Informatique',
            'photo': '',
            'date_inscription': '2024-01-15T10:30:00',
            'statut': 'actif'
        },
        {
            'id': 'stg-002',
            'nom': 'Benali',
            'prenom': 'Fatima',
            'email': '<EMAIL>',
            'telephone': '0662345678',
            'date_naissance': '1996-07-22',
            'adresse': 'Hay Maarif, Casablanca 20000',
            'niveau_etude': 'bac+2',
            'specialite': 'Gestion',
            'photo': '',
            'date_inscription': '2024-02-01T14:15:00',
            'statut': 'actif'
        },
        {
            'id': 'stg-003',
            'nom': 'Al-Hassani',
            'prenom': 'Abderrahman',
            'email': '<EMAIL>',
            'telephone': '0663456789',
            'date_naissance': '1994-11-08',
            'adresse': 'Hay Agdal, Rabat 10000',
            'niveau_etude': 'bac+5',
            'specialite': 'Ingénierie',
            'photo': '',
            'date_inscription': '2024-01-20T09:45:00',
            'statut': 'actif'
        },
        {
            'id': 'stg-004',
            'nom': 'Az-Zahrani',
            'prenom': 'Khadija',
            'email': '<EMAIL>',
            'telephone': '0664567890',
            'date_naissance': '1997-05-12',
            'adresse': 'Hay Andalous, Fès 30000',
            'niveau_etude': 'bac+4',
            'specialite': 'Finance',
            'photo': '',
            'date_inscription': '2024-02-15T11:20:00',
            'statut': 'actif'
        },
        {
            'id': 'stg-005',
            'nom': 'Al-Alaoui',
            'prenom': 'Youssef',
            'email': '<EMAIL>',
            'telephone': '0665678901',
            'date_naissance': '1995-09-30',
            'adresse': 'Hay Gueliz, Marrakech 40000',
            'niveau_etude': 'bac+3',
            'specialite': 'Ressources Humaines',
            'photo': '',
            'date_inscription': '2024-03-01T09:15:00',
            'statut': 'actif'
        }
    ],
    'stages': [
        {
            'id': 'stage-001',
            'stagiaire_id': 'stg-001',
            'type_stage_id': 1,
            'promotion_id': 'promo-001',
            'date_debut': '2024-03-01',
            'date_fin': '2024-08-31',
            'statut': 'en_cours',
            'unite_affectation': 'Service Informatique',
            'responsable': 'Pr. Ahmed Al-Karimi',
            'objectifs': 'Développement d\'applications web et maintenance système',
            'date_creation': '2024-02-15T10:00:00'
        },
        {
            'id': 'stage-002',
            'stagiaire_id': 'stg-002',
            'type_stage_id': 2,
            'promotion_id': 'promo-001',
            'date_debut': '2024-04-01',
            'date_fin': '2024-09-30',
            'statut': 'en_cours',
            'unite_affectation': 'Service Ressources Humaines',
            'responsable': 'Dr. Nadia Al-Fassi',
            'objectifs': 'Gestion administrative et recrutement',
            'date_creation': '2024-03-01T11:30:00'
        }
    ],
    'promotions': [
        {
            'id': 'promo-001',
            'annee': 2024,
            'filiere': 'Informatique et Gestion',
            'nom': 'Promotion 2024-A',
            'date_debut': '2024-03-01',
            'date_fin': '2024-12-31',
            'effectif_max': 50,
            'statut': 'active',
            'description': 'Promotion mixte informatique et gestion pour l\'année 2024',
            'date_creation': '2024-01-10T08:00:00'
        },
        {
            'id': 'promo-002',
            'annee': 2024,
            'filiere': 'Ingénierie',
            'nom': 'Promotion 2024-B',
            'date_debut': '2024-09-01',
            'date_fin': '2025-06-30',
            'effectif_max': 30,
            'statut': 'planifiee',
            'description': 'Promotion spécialisée en ingénierie pour 2024-2025',
            'date_creation': '2024-01-15T09:00:00'
        }
    ],
    'types_stages': [
        {
            'id': 1,
            'libelle': 'Stage d\'observation',
            'conditions_admission': 'Niveau Bac minimum, âge entre 18-25 ans'
        },
        {
            'id': 2,
            'libelle': 'Stage de formation',
            'conditions_admission': 'Diplôme universitaire, expérience préalable souhaitée'
        },
        {
            'id': 3,
            'libelle': 'Stage de perfectionnement',
            'conditions_admission': 'Expérience professionnelle de 2 ans minimum'
        }
    ],
    'documents': [
        {
            'id': 'doc-001',
            'nom': 'Convention_Mohammed_Al-Idrissi_2024.pdf',
            'type': 'convention',
            'format': 'pdf',
            'stagiaire_id': 'stg-001',
            'taille': '245 KB',
            'date_creation': '2024-02-20T15:30:00',
            'statut': 'generated',
            'chemin_fichier': '/documents/conventions/Convention_Jean_Dupont_2024.pdf'
        },
        {
            'id': 'doc-002',
            'nom': 'Liste_Stagiaires_Promo_2024A.xlsx',
            'type': 'liste',
            'format': 'excel',
            'promotion_id': 'promo-001',
            'taille': '89 KB',
            'date_creation': '2024-03-01T10:15:00',
            'statut': 'generated',
            'chemin_fichier': '/documents/listes/Liste_Stagiaires_Promo_2024A.xlsx'
        }
    ],
    'notifications': [
        {
            'id': 'notif-001',
            'contenu': 'Stage de Mohammed Al-Idrissi expire dans 3 jours',
            'type': 'warning',
            'date_envoi': '2024-06-16T09:00:00',
            'stagiaire_id': 'stg-001',
            'lu': False
        },
        {
            'id': 'notif-002',
            'contenu': 'Nouveau stagiaire inscrit: Fatima Benali',
            'type': 'info',
            'date_envoi': '2024-02-01T14:20:00',
            'stagiaire_id': 'stg-002',
            'lu': True
        }
    ],
    'utilisateurs': [
        {
            'id': 1,
            'nom_user': 'Admin',
            'email': '<EMAIL>',
            'role': 'admin'
        }
    ],
    'actions': []
}

# Routes principales
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html')

@app.route('/stagiaires')
def stagiaires():
    return render_template('stagiaires.html')

@app.route('/stages')
def stages():
    return render_template('stages.html')

@app.route('/promotions')
def promotions():
    return render_template('promotions.html')

@app.route('/documents')
def documents():
    return render_template('documents.html')

@app.route('/chatbot')
def chatbot():
    return render_template('chatbot.html')

# API Routes
@app.route('/api/stagiaires', methods=['GET', 'POST'])
def api_stagiaires():
    if request.method == 'GET':
        return jsonify(data_store['stagiaires'])
    
    elif request.method == 'POST':
        data = request.get_json()
        stagiaire = {
            'id': str(uuid.uuid4()),
            'nom': data.get('nom'),
            'prenom': data.get('prenom'),
            'email': data.get('email'),
            'telephone': data.get('telephone'),
            'date_naissance': data.get('date_naissance'),
            'adresse': data.get('adresse'),
            'niveau_etude': data.get('niveau_etude'),
            'specialite': data.get('specialite'),
            'photo': data.get('photo', ''),
            'date_inscription': datetime.now().isoformat(),
            'statut': 'actif'
        }
        data_store['stagiaires'].append(stagiaire)
        
        # Log action
        log_action('creation_stagiaire', f"Création du stagiaire {stagiaire['nom']} {stagiaire['prenom']}")
        
        return jsonify(stagiaire), 201

@app.route('/api/stagiaires/<stagiaire_id>', methods=['GET', 'PUT', 'DELETE'])
def api_stagiaire_detail(stagiaire_id):
    stagiaire = next((s for s in data_store['stagiaires'] if s['id'] == stagiaire_id), None)
    
    if not stagiaire:
        return jsonify({'error': 'Stagiaire non trouvé'}), 404
    
    if request.method == 'GET':
        return jsonify(stagiaire)
    
    elif request.method == 'PUT':
        data = request.get_json()
        stagiaire.update(data)
        log_action('modification_stagiaire', f"Modification du stagiaire {stagiaire['nom']} {stagiaire['prenom']}")
        return jsonify(stagiaire)
    
    elif request.method == 'DELETE':
        data_store['stagiaires'].remove(stagiaire)
        log_action('suppression_stagiaire', f"Suppression du stagiaire {stagiaire['nom']} {stagiaire['prenom']}")
        return jsonify({'message': 'Stagiaire supprimé'}), 200

@app.route('/api/stages', methods=['GET', 'POST'])
def api_stages():
    if request.method == 'GET':
        return jsonify(data_store['stages'])
    
    elif request.method == 'POST':
        data = request.get_json()
        stage = {
            'id': str(uuid.uuid4()),
            'stagiaire_id': data.get('stagiaire_id'),
            'type_stage_id': data.get('type_stage_id'),
            'promotion_id': data.get('promotion_id'),
            'date_debut': data.get('date_debut'),
            'date_fin': data.get('date_fin'),
            'statut': data.get('statut', 'en_cours'),
            'unite_affectation': data.get('unite_affectation'),
            'responsable': data.get('responsable'),
            'objectifs': data.get('objectifs'),
            'date_creation': datetime.now().isoformat()
        }
        data_store['stages'].append(stage)
        log_action('creation_stage', f"Création d'un nouveau stage")
        return jsonify(stage), 201

@app.route('/api/promotions', methods=['GET', 'POST'])
def api_promotions():
    if request.method == 'GET':
        return jsonify(data_store['promotions'])
    
    elif request.method == 'POST':
        data = request.get_json()
        promotion = {
            'id': str(uuid.uuid4()),
            'annee': data.get('annee'),
            'filiere': data.get('filiere'),
            'nom': data.get('nom'),
            'date_debut': data.get('date_debut'),
            'date_fin': data.get('date_fin'),
            'effectif_max': data.get('effectif_max', 50),
            'statut': data.get('statut', 'active'),
            'date_creation': datetime.now().isoformat()
        }
        data_store['promotions'].append(promotion)
        log_action('creation_promotion', f"Création de la promotion {promotion['nom']}")
        return jsonify(promotion), 201

@app.route('/api/types-stages')
def api_types_stages():
    return jsonify(data_store['types_stages'])

@app.route('/api/notifications')
def api_notifications():
    return jsonify(data_store['notifications'])

@app.route('/api/search')
def api_search():
    query = request.args.get('q', '').lower()
    results = {
        'stagiaires': [],
        'stages': [],
        'promotions': []
    }
    
    if query:
        # Recherche dans les stagiaires
        for stagiaire in data_store['stagiaires']:
            if (query in stagiaire.get('nom', '').lower() or 
                query in stagiaire.get('prenom', '').lower() or
                query in stagiaire.get('email', '').lower()):
                results['stagiaires'].append(stagiaire)
        
        # Recherche dans les stages
        for stage in data_store['stages']:
            if (query in stage.get('unite_affectation', '').lower() or
                query in stage.get('responsable', '').lower()):
                results['stages'].append(stage)
        
        # Recherche dans les promotions
        for promotion in data_store['promotions']:
            if (query in promotion.get('nom', '').lower() or
                query in promotion.get('filiere', '').lower()):
                results['promotions'].append(promotion)
    
    return jsonify(results)

@app.route('/api/stages/<stage_id>', methods=['GET', 'PUT', 'DELETE'])
def api_stage_detail(stage_id):
    stage = next((s for s in data_store['stages'] if s['id'] == stage_id), None)

    if not stage:
        return jsonify({'error': 'Stage non trouvé'}), 404

    if request.method == 'GET':
        return jsonify(stage)

    elif request.method == 'PUT':
        data = request.get_json()
        stage.update(data)
        log_action('modification_stage', f"Modification du stage {stage_id}")
        return jsonify(stage)

    elif request.method == 'DELETE':
        data_store['stages'].remove(stage)
        log_action('suppression_stage', f"Suppression du stage {stage_id}")
        return jsonify({'message': 'Stage supprimé'}), 200

@app.route('/api/promotions/<promotion_id>', methods=['GET', 'PUT', 'DELETE'])
def api_promotion_detail(promotion_id):
    promotion = next((p for p in data_store['promotions'] if p['id'] == promotion_id), None)

    if not promotion:
        return jsonify({'error': 'Promotion non trouvée'}), 404

    if request.method == 'GET':
        return jsonify(promotion)

    elif request.method == 'PUT':
        data = request.get_json()
        promotion.update(data)
        log_action('modification_promotion', f"Modification de la promotion {promotion['nom']}")
        return jsonify(promotion)

    elif request.method == 'DELETE':
        data_store['promotions'].remove(promotion)
        log_action('suppression_promotion', f"Suppression de la promotion {promotion['nom']}")
        return jsonify({'message': 'Promotion supprimée'}), 200

@app.route('/api/stats')
def api_stats():
    stats = {
        'total_stagiaires': len(data_store['stagiaires']),
        'total_stages': len(data_store['stages']),
        'total_promotions': len(data_store['promotions']),
        'stages_actifs': len([s for s in data_store['stages'] if s.get('statut') == 'en_cours']),
        'stagiaires_actifs': len([s for s in data_store['stagiaires'] if s.get('statut') == 'actif'])
    }
    return jsonify(stats)

# Routes pour la gestion des documents
@app.route('/api/documents', methods=['GET', 'POST'])
def api_documents():
    if request.method == 'GET':
        return jsonify(data_store['documents'])

    elif request.method == 'POST':
        data = request.get_json()
        doc_type = data.get('type')
        doc_format = data.get('format')

        try:
            # Générer le document selon le type et format
            if doc_type == 'convention' and doc_format == 'pdf':
                file_path = generate_convention_pdf(data)
            elif doc_type == 'attestation' and doc_format == 'pdf':
                file_path = generate_attestation_pdf(data)
            elif doc_type == 'liste' and doc_format == 'excel':
                file_path = generate_liste_excel(data)
            elif doc_type == 'liste' and doc_format == 'pdf':
                file_path = generate_liste_pdf(data)
            else:
                return jsonify({'error': 'Type ou format non supporté'}), 400

            # Créer l'entrée document
            document = {
                'id': str(uuid.uuid4()),
                'nom': os.path.basename(file_path),
                'type': doc_type,
                'format': doc_format,
                'stagiaire_id': data.get('stagiaire_id'),
                'promotion_id': data.get('promotion_id'),
                'stage_id': data.get('stage_id'),
                'taille': get_file_size(file_path),
                'date_creation': datetime.now().isoformat(),
                'statut': 'generated',
                'chemin_fichier': file_path
            }

            data_store['documents'].append(document)
            log_action('generation_document', f"Génération du document {document['nom']}")

            return jsonify(document), 201

        except Exception as e:
            return jsonify({'error': f'Erreur lors de la génération: {str(e)}'}), 500

@app.route('/api/documents/<doc_id>/download')
def download_document(doc_id):
    document = next((d for d in data_store['documents'] if d['id'] == doc_id), None)

    if not document:
        return jsonify({'error': 'Document non trouvé'}), 404

    file_path = document['chemin_fichier']
    if not os.path.exists(file_path):
        return jsonify({'error': 'Fichier non trouvé'}), 404

    return send_file(file_path, as_attachment=True, download_name=document['nom'])

@app.route('/api/documents/<doc_id>', methods=['DELETE'])
def delete_document(doc_id):
    document = next((d for d in data_store['documents'] if d['id'] == doc_id), None)

    if not document:
        return jsonify({'error': 'Document non trouvé'}), 404

    # Supprimer le fichier physique
    try:
        if os.path.exists(document['chemin_fichier']):
            os.remove(document['chemin_fichier'])
    except Exception as e:
        print(f"Erreur lors de la suppression du fichier: {e}")

    # Supprimer de la base de données
    data_store['documents'].remove(document)
    log_action('suppression_document', f"Suppression du document {document['nom']}")

    return jsonify({'message': 'Document supprimé'}), 200

@app.route('/api/export/stagiaires')
def export_stagiaires():
    format_type = request.args.get('format', 'excel')
    promotion_id = request.args.get('promotion_id')

    try:
        if format_type == 'excel':
            file_path = export_stagiaires_excel(promotion_id)
        elif format_type == 'pdf':
            file_path = export_stagiaires_pdf(promotion_id)
        else:
            return jsonify({'error': 'Format non supporté'}), 400

        return send_file(file_path, as_attachment=True, download_name=os.path.basename(file_path))

    except Exception as e:
        return jsonify({'error': f'Erreur lors de l\'export: {str(e)}'}), 500

@app.route('/api/bulk-download')
def bulk_download():
    doc_ids = request.args.getlist('ids')

    if not doc_ids:
        return jsonify({'error': 'Aucun document sélectionné'}), 400

    try:
        # Créer un fichier ZIP temporaire
        temp_dir = tempfile.mkdtemp()
        zip_path = os.path.join(temp_dir, f'documents_{datetime.now().strftime("%Y%m%d_%H%M%S")}.zip')

        with zipfile.ZipFile(zip_path, 'w') as zip_file:
            for doc_id in doc_ids:
                document = next((d for d in data_store['documents'] if d['id'] == doc_id), None)
                if document and os.path.exists(document['chemin_fichier']):
                    zip_file.write(document['chemin_fichier'], document['nom'])

        return send_file(zip_path, as_attachment=True, download_name=os.path.basename(zip_path))

    except Exception as e:
        return jsonify({'error': f'Erreur lors de la création de l\'archive: {str(e)}'}), 500

@app.route('/api/search')
def global_search():
    query = request.args.get('q', '').lower()

    if not query or len(query) < 2:
        return jsonify({'results': []})

    results = []

    # Recherche dans les stagiaires
    for stagiaire in data_store['stagiaires']:
        if (query in stagiaire['nom'].lower() or
            query in stagiaire['prenom'].lower() or
            query in stagiaire['email'].lower()):
            results.append({
                'type': 'stagiaire',
                'id': stagiaire['id'],
                'title': f"{stagiaire['nom']} {stagiaire['prenom']}",
                'subtitle': stagiaire['email'],
                'url': f'/stagiaires?highlight={stagiaire["id"]}',
                'icon': 'fas fa-user'
            })

    # Recherche dans les stages
    for stage in data_store['stages']:
        stagiaire = next((s for s in data_store['stagiaires'] if s['id'] == stage['stagiaire_id']), None)
        if (query in stage['unite_affectation'].lower() or
            query in stage['responsable'].lower() or
            (stagiaire and query in f"{stagiaire['nom']} {stagiaire['prenom']}".lower())):
            results.append({
                'type': 'stage',
                'id': stage['id'],
                'title': f"Stage - {stage['unite_affectation']}",
                'subtitle': f"Responsable: {stage['responsable']}",
                'url': f'/stages?highlight={stage["id"]}',
                'icon': 'fas fa-briefcase'
            })

    # Recherche dans les promotions
    for promotion in data_store['promotions']:
        if (query in promotion['nom'].lower() or
            query in promotion['filiere'].lower() or
            query in str(promotion['annee'])):
            results.append({
                'type': 'promotion',
                'id': promotion['id'],
                'title': promotion['nom'],
                'subtitle': f"{promotion['filiere']} - {promotion['annee']}",
                'url': f'/promotions?highlight={promotion["id"]}',
                'icon': 'fas fa-graduation-cap'
            })

    # Recherche dans les documents
    for document in data_store['documents']:
        if query in document['nom'].lower():
            results.append({
                'type': 'document',
                'id': document['id'],
                'title': document['nom'],
                'subtitle': f"{document['type']} - {document['format']}",
                'url': f'/documents?highlight={document["id"]}',
                'icon': 'fas fa-file'
            })

    # Limiter les résultats
    results = results[:10]

    return jsonify({'results': results})

# Fonctions de génération de documents
def generate_convention_pdf(data):
    """Génère une convention de stage en PDF"""
    stagiaire_id = data.get('stagiaire_id')
    stage_id = data.get('stage_id')

    stagiaire = next((s for s in data_store['stagiaires'] if s['id'] == stagiaire_id), None)
    stage = next((s for s in data_store['stages'] if s['id'] == stage_id), None)

    if not stagiaire or not stage:
        raise ValueError("Stagiaire ou stage non trouvé")

    # Créer le dossier s'il n'existe pas
    os.makedirs('uploads/conventions', exist_ok=True)

    filename = f"Convention_{stagiaire['nom']}_{stagiaire['prenom']}_{datetime.now().strftime('%Y%m%d')}.pdf"
    file_path = os.path.join('uploads/conventions', filename)

    # Créer le document PDF
    doc = SimpleDocTemplate(file_path, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []

    # Titre
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Centré
    )
    story.append(Paragraph("CONVENTION DE STAGE", title_style))
    story.append(Spacer(1, 20))

    # Informations du stagiaire
    story.append(Paragraph("<b>INFORMATIONS DU STAGIAIRE</b>", styles['Heading2']))
    stagiaire_data = [
        ['Nom:', stagiaire['nom']],
        ['Prénom:', stagiaire['prenom']],
        ['Email:', stagiaire['email']],
        ['Téléphone:', stagiaire.get('telephone', 'Non renseigné')],
        ['Niveau d\'étude:', stagiaire.get('niveau_etude', 'Non renseigné')],
        ['Spécialité:', stagiaire.get('specialite', 'Non renseigné')]
    ]

    stagiaire_table = Table(stagiaire_data, colWidths=[2*inch, 4*inch])
    stagiaire_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    story.append(stagiaire_table)
    story.append(Spacer(1, 20))

    # Informations du stage
    story.append(Paragraph("<b>INFORMATIONS DU STAGE</b>", styles['Heading2']))
    stage_data = [
        ['Date de début:', stage['date_debut']],
        ['Date de fin:', stage['date_fin']],
        ['Unité d\'affectation:', stage['unite_affectation']],
        ['Responsable:', stage['responsable']],
        ['Statut:', stage['statut']]
    ]

    if stage.get('objectifs'):
        stage_data.append(['Objectifs:', stage['objectifs']])

    stage_table = Table(stage_data, colWidths=[2*inch, 4*inch])
    stage_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    story.append(stage_table)
    story.append(Spacer(1, 30))

    # Signatures
    story.append(Paragraph("<b>SIGNATURES</b>", styles['Heading2']))
    signature_data = [
        ['Le stagiaire:', 'Le responsable:'],
        ['', ''],
        ['', ''],
        [f"{stagiaire['prenom']} {stagiaire['nom']}", stage['responsable']]
    ]

    signature_table = Table(signature_data, colWidths=[3*inch, 3*inch])
    signature_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('LINEBELOW', (0, 2), (-1, 2), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'TOP')
    ]))
    story.append(signature_table)

    # Footer
    story.append(Spacer(1, 30))
    footer_text = f"Document généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}"
    story.append(Paragraph(footer_text, styles['Normal']))

    doc.build(story)
    return file_path

def generate_attestation_pdf(data):
    """Génère une attestation de stage en PDF"""
    stagiaire_id = data.get('stagiaire_id')
    stage_id = data.get('stage_id')

    stagiaire = next((s for s in data_store['stagiaires'] if s['id'] == stagiaire_id), None)
    stage = next((s for s in data_store['stages'] if s['id'] == stage_id), None)

    if not stagiaire or not stage:
        raise ValueError("Stagiaire ou stage non trouvé")

    # Créer le dossier s'il n'existe pas
    os.makedirs('uploads/attestations', exist_ok=True)

    filename = f"Attestation_{stagiaire['nom']}_{stagiaire['prenom']}_{datetime.now().strftime('%Y%m%d')}.pdf"
    file_path = os.path.join('uploads/attestations', filename)

    # Créer le document PDF
    doc = SimpleDocTemplate(file_path, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []

    # Titre
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=20,
        spaceAfter=40,
        alignment=1  # Centré
    )
    story.append(Paragraph("ATTESTATION DE STAGE", title_style))
    story.append(Spacer(1, 30))

    # Corps de l'attestation
    body_style = ParagraphStyle(
        'CustomBody',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=15,
        alignment=4  # Justifié
    )

    attestation_text = f"""
    Je soussigné(e), <b>{stage['responsable']}</b>, responsable du service <b>{stage['unite_affectation']}</b>,
    atteste que <b>Monsieur/Madame {stagiaire['prenom']} {stagiaire['nom']}</b> a effectué un stage
    dans notre service du <b>{stage['date_debut']}</b> au <b>{stage['date_fin']}</b>.
    """

    if stage.get('objectifs'):
        attestation_text += f"""
        <br/><br/>
        Ce stage avait pour objectifs : <b>{stage['objectifs']}</b>
        """

    attestation_text += f"""
    <br/><br/>
    Le stagiaire a fait preuve de sérieux et de motivation tout au long de cette période.
    <br/><br/>
    Cette attestation est délivrée pour servir et valoir ce que de droit.
    """

    story.append(Paragraph(attestation_text, body_style))
    story.append(Spacer(1, 50))

    # Date et lieu
    date_text = f"Fait le {datetime.now().strftime('%d/%m/%Y')}"
    story.append(Paragraph(date_text, styles['Normal']))
    story.append(Spacer(1, 30))

    # Signature
    signature_text = f"<b>{stage['responsable']}</b><br/>Responsable {stage['unite_affectation']}"
    signature_style = ParagraphStyle(
        'Signature',
        parent=styles['Normal'],
        fontSize=12,
        alignment=2  # Droite
    )
    story.append(Paragraph(signature_text, signature_style))

    doc.build(story)
    return file_path

def generate_liste_excel(data):
    """Génère une liste de stagiaires en Excel"""
    promotion_id = data.get('promotion_id')

    # Créer le dossier s'il n'existe pas
    os.makedirs('uploads/listes', exist_ok=True)

    # Filtrer les stagiaires selon la promotion
    if promotion_id:
        promotion = next((p for p in data_store['promotions'] if p['id'] == promotion_id), None)
        if not promotion:
            raise ValueError("Promotion non trouvée")

        # Récupérer les stagiaires de cette promotion via les stages
        stages_promotion = [s for s in data_store['stages'] if s.get('promotion_id') == promotion_id]
        stagiaires_ids = [s['stagiaire_id'] for s in stages_promotion]
        stagiaires = [s for s in data_store['stagiaires'] if s['id'] in stagiaires_ids]

        filename = f"Liste_Stagiaires_{promotion['nom'].replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.xlsx"
    else:
        stagiaires = data_store['stagiaires']
        filename = f"Liste_Tous_Stagiaires_{datetime.now().strftime('%Y%m%d')}.xlsx"

    file_path = os.path.join('uploads/listes', filename)

    # Créer le workbook Excel
    wb = Workbook()
    ws = wb.active
    ws.title = "Liste des Stagiaires"

    # En-têtes
    headers = ['Nom', 'Prénom', 'Email', 'Téléphone', 'Date de naissance', 'Niveau d\'étude',
               'Spécialité', 'Date d\'inscription', 'Statut']

    # Style pour les en-têtes
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")

    # Ajouter les en-têtes
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment

    # Ajouter les données
    for row, stagiaire in enumerate(stagiaires, 2):
        ws.cell(row=row, column=1, value=stagiaire['nom'])
        ws.cell(row=row, column=2, value=stagiaire['prenom'])
        ws.cell(row=row, column=3, value=stagiaire['email'])
        ws.cell(row=row, column=4, value=stagiaire.get('telephone', ''))
        ws.cell(row=row, column=5, value=stagiaire.get('date_naissance', ''))
        ws.cell(row=row, column=6, value=stagiaire.get('niveau_etude', ''))
        ws.cell(row=row, column=7, value=stagiaire.get('specialite', ''))
        ws.cell(row=row, column=8, value=stagiaire.get('date_inscription', ''))
        ws.cell(row=row, column=9, value=stagiaire.get('statut', ''))

    # Ajuster la largeur des colonnes
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Ajouter des informations supplémentaires
    if promotion_id and promotion:
        ws.cell(row=len(stagiaires) + 3, column=1, value=f"Promotion: {promotion['nom']}")
        ws.cell(row=len(stagiaires) + 4, column=1, value=f"Année: {promotion['annee']}")
        ws.cell(row=len(stagiaires) + 5, column=1, value=f"Filière: {promotion['filiere']}")

    ws.cell(row=len(stagiaires) + 7, column=1, value=f"Total stagiaires: {len(stagiaires)}")
    ws.cell(row=len(stagiaires) + 8, column=1, value=f"Généré le: {datetime.now().strftime('%d/%m/%Y à %H:%M')}")

    wb.save(file_path)
    return file_path

def generate_liste_pdf(data):
    """Génère une liste de stagiaires en PDF"""
    promotion_id = data.get('promotion_id')

    # Créer le dossier s'il n'existe pas
    os.makedirs('uploads/listes', exist_ok=True)

    # Filtrer les stagiaires selon la promotion
    if promotion_id:
        promotion = next((p for p in data_store['promotions'] if p['id'] == promotion_id), None)
        if not promotion:
            raise ValueError("Promotion non trouvée")

        stages_promotion = [s for s in data_store['stages'] if s.get('promotion_id') == promotion_id]
        stagiaires_ids = [s['stagiaire_id'] for s in stages_promotion]
        stagiaires = [s for s in data_store['stagiaires'] if s['id'] in stagiaires_ids]

        filename = f"Liste_Stagiaires_{promotion['nom'].replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.pdf"
        title = f"Liste des Stagiaires - {promotion['nom']}"
    else:
        stagiaires = data_store['stagiaires']
        filename = f"Liste_Tous_Stagiaires_{datetime.now().strftime('%Y%m%d')}.pdf"
        title = "Liste de Tous les Stagiaires"

    file_path = os.path.join('uploads/listes', filename)

    # Créer le document PDF
    doc = SimpleDocTemplate(file_path, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []

    # Titre
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=30,
        alignment=1  # Centré
    )
    story.append(Paragraph(title, title_style))

    # Informations de la promotion si applicable
    if promotion_id and promotion:
        info_style = ParagraphStyle(
            'InfoStyle',
            parent=styles['Normal'],
            fontSize=10,
            spaceAfter=20,
            alignment=1
        )
        info_text = f"Année: {promotion['annee']} | Filière: {promotion['filiere']}"
        story.append(Paragraph(info_text, info_style))

    # Tableau des stagiaires
    table_data = [['Nom', 'Prénom', 'Email', 'Téléphone', 'Niveau', 'Statut']]

    for stagiaire in stagiaires:
        table_data.append([
            stagiaire['nom'],
            stagiaire['prenom'],
            stagiaire['email'],
            stagiaire.get('telephone', '-'),
            stagiaire.get('niveau_etude', '-'),
            stagiaire.get('statut', '-')
        ])

    # Créer le tableau
    table = Table(table_data, colWidths=[1.2*inch, 1.2*inch, 2*inch, 1*inch, 0.8*inch, 0.8*inch])
    table.setStyle(TableStyle([
        # En-tête
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),

        # Corps du tableau
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 8),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),

        # Alternance de couleurs
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
    ]))

    story.append(table)
    story.append(Spacer(1, 30))

    # Résumé
    summary_text = f"<b>Total: {len(stagiaires)} stagiaire(s)</b>"
    story.append(Paragraph(summary_text, styles['Normal']))

    # Footer
    story.append(Spacer(1, 20))
    footer_text = f"Document généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}"
    story.append(Paragraph(footer_text, styles['Normal']))

    doc.build(story)
    return file_path

def export_stagiaires_excel(promotion_id=None):
    """Export Excel des stagiaires"""
    return generate_liste_excel({'promotion_id': promotion_id})

def export_stagiaires_pdf(promotion_id=None):
    """Export PDF des stagiaires"""
    return generate_liste_pdf({'promotion_id': promotion_id})

def get_file_size(file_path):
    """Retourne la taille du fichier formatée"""
    try:
        size = os.path.getsize(file_path)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    except:
        return "0 B"

def log_action(type_action, description):
    action = {
        'id': str(uuid.uuid4()),
        'type_action': type_action,
        'description': description,
        'date_action': datetime.now().isoformat(),
        'utilisateur': 'Admin'  # À adapter selon l'authentification
    }
    data_store['actions'].append(action)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
