# StageManager - Application Web de Gestion des Stages

Une application web moderne et sophistiquée pour la gestion complète des stages, développée avec Flask et une interface utilisateur interactive.

## 🚀 Fonctionnalités

### 📊 Tableau de bord
- Statistiques en temps réel
- Graphiques interactifs (Chart.js)
- Activités récentes
- Alertes et échéances
- Actions rapides (FAB)

### 👥 Gestion des Stagiaires
- Inscription avec formulaire complet
- Fiche détaillée avec historique
- Recherche et filtres avancés
- Vue tableau et cartes
- Gestion des photos de profil

### 💼 Gestion des Stages
- Création et suivi des stages
- Types de stages configurables
- Affectation par unité
- Vue timeline des stages
- Calcul automatique de progression

### 🎓 Gestion des Promotions
- Organisation par promotions
- Gestion des effectifs
- Suivi des inscriptions
- Statistiques par promotion

### 📄 Génération de Documents
- Conventions de stage automatisées
- Attestations de fin de stage
- Rapports personnalisables
- Export PDF, Excel, Word
- Modèles configurables

### 🤖 Assistant IA (Chatbot)
- Interface conversationnelle
- Aide contextuelle
- Génération automatique de réponses
- Suggestions intelligentes
- Historique des conversations

### 🔔 Système de Notifications
- Alertes automatiques
- Suivi des échéances
- Notifications en temps réel
- Gestion des rappels

## 🛠️ Technologies Utilisées

### Backend
- **Flask** - Framework web Python
- **Python 3.12+** - Langage de programmation
- **Flask-CORS** - Gestion des CORS

### Frontend
- **HTML5** - Structure
- **CSS3** - Styles modernes avec variables CSS
- **JavaScript ES6+** - Logique interactive
- **Chart.js** - Graphiques et visualisations
- **Font Awesome** - Icônes
- **Google Fonts** - Typographie (Inter)

### Fonctionnalités
- **Responsive Design** - Compatible mobile/tablette/desktop
- **Progressive Web App** - Fonctionnalités PWA
- **Animations CSS** - Transitions fluides
- **Thème sombre/clair** - Interface adaptable

## 📁 Structure du Projet

```
instruction/
├── app.py                 # Application Flask principale
├── templates/             # Templates HTML
│   ├── base.html         # Template de base
│   ├── index.html        # Page d'accueil
│   ├── dashboard.html    # Tableau de bord
│   ├── stagiaires.html   # Gestion des stagiaires
│   ├── stages.html       # Gestion des stages
│   ├── promotions.html   # Gestion des promotions
│   ├── documents.html    # Gestion des documents
│   └── chatbot.html      # Assistant IA
├── static/               # Fichiers statiques
│   ├── css/
│   │   └── style.css     # Styles principaux
│   ├── js/
│   │   ├── app.js        # Application principale
│   │   ├── stagiaires.js # Gestion des stagiaires
│   │   ├── stages.js     # Gestion des stages
│   │   ├── promotions.js # Gestion des promotions
│   │   ├── documents.js  # Gestion des documents
│   │   └── chatbot.js    # Assistant IA
│   └── images/           # Images et assets
├── uploads/              # Fichiers uploadés
└── README.md            # Documentation
```

## 🚀 Installation et Lancement

### Prérequis
- Python 3.12 ou supérieur
- pip (gestionnaire de paquets Python)

### Installation
1. Cloner ou télécharger le projet
2. Installer les dépendances :
```bash
pip install flask flask-cors
```

### Lancement
```bash
python app.py
```

L'application sera accessible à l'adresse : `http://localhost:5000`

## 📱 Utilisation

### Page d'Accueil
- Interface d'accueil moderne avec présentation des fonctionnalités
- Accès direct au tableau de bord
- Design responsive avec animations

### Tableau de Bord
- Vue d'ensemble des statistiques
- Graphiques interactifs
- Activités récentes et alertes
- Actions rapides via le bouton FAB

### Gestion des Stagiaires
- **Ajouter** : Formulaire complet avec validation
- **Rechercher** : Filtres par nom, statut, niveau, promotion
- **Visualiser** : Vue tableau ou cartes
- **Modifier** : Édition en ligne des informations
- **Supprimer** : Suppression avec confirmation

### Gestion des Stages
- **Créer** : Formulaire avec sélection stagiaire/type/unité
- **Suivre** : Progression automatique basée sur les dates
- **Visualiser** : Vue tableau, cartes ou timeline
- **Gérer** : Modification des statuts et informations

### Gestion des Promotions
- **Organiser** : Création de promotions par année/filière
- **Affecter** : Attribution de stagiaires aux promotions
- **Suivre** : Statistiques d'effectifs et progression
- **Exporter** : Listes et rapports

### Génération de Documents
- **Types** : Convention, Attestation, Rapport, Liste
- **Formats** : PDF, Excel, Word
- **Options** : Signatures, filigranes, envoi automatique
- **Aperçu** : Prévisualisation avant génération

### Assistant IA
- **Conversation** : Interface de chat intuitive
- **Aide** : Réponses contextuelles aux questions
- **Suggestions** : Propositions d'actions rapides
- **Paramètres** : Configuration du comportement

## 🎨 Design et UX

### Principes de Design
- **Moderne** : Interface épurée et contemporaine
- **Intuitive** : Navigation claire et logique
- **Responsive** : Adaptation à tous les écrans
- **Accessible** : Respect des standards d'accessibilité

### Palette de Couleurs
- **Primaire** : Bleu (#4f46e5)
- **Secondaire** : Vert (#10b981)
- **Accent** : Orange (#f59e0b)
- **Neutre** : Gris (#64748b)

### Typographie
- **Police** : Inter (Google Fonts)
- **Hiérarchie** : Tailles et poids cohérents
- **Lisibilité** : Contraste optimisé

## 🔧 Configuration

### Données d'Exemple
L'application inclut des données d'exemple pour la démonstration :
- 3 stagiaires pré-enregistrés
- 2 stages en cours
- 2 promotions (active et planifiée)
- Documents générés
- Notifications d'exemple

### Personnalisation
- **Couleurs** : Modifier les variables CSS dans `style.css`
- **Logo** : Remplacer dans le header de `base.html`
- **Données** : Modifier `data_store` dans `app.py`

## 🚀 Fonctionnalités Avancées

### Recherche Intelligente
- Recherche globale dans l'en-tête
- Filtres spécifiques par module
- Suggestions automatiques

### Notifications
- Système d'alertes en temps réel
- Badges de notification
- Historique des notifications

### Export et Import
- Export PDF/Excel des listes
- Génération de documents automatisée
- Import de données (à développer)

### Responsive Design
- Adaptation mobile complète
- Menu hamburger sur petits écrans
- Optimisation tactile

## 🔮 Évolutions Futures

### Base de Données
- Migration vers PostgreSQL/MySQL
- Modèles de données complets
- Migrations automatiques

### Authentification
- Système de connexion
- Gestion des rôles et permissions
- Sécurité renforcée

### API REST
- Endpoints complets
- Documentation Swagger
- Versioning API

### Intégrations
- Connexion SIRH
- API externes
- Webhooks

### IA Avancée
- Chatbot avec IA réelle
- Analyse prédictive
- Recommandations intelligentes

## 📄 Licence

Ce projet est développé à des fins éducatives et de démonstration.

## 👨‍💻 Développement

Développé avec ❤️ en utilisant les meilleures pratiques de développement web moderne.

### Contribution
Les contributions sont les bienvenues ! N'hésitez pas à :
- Signaler des bugs
- Proposer des améliorations
- Ajouter de nouvelles fonctionnalités

---

**StageManager** - Simplifiez la gestion de vos stages avec style et efficacité ! 🚀
