/* Horloge Numérique Intég<PERSON>e */
.digital-clock {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-family: 'Orbitron', monospace;
    font-size: 0.875rem;
    font-weight: 600;
    color: white;
    backdrop-filter: blur(10px);
    user-select: none;
    text-align: center;
    animation: none; /* Supprime l'animation pour l'intégration */
}

/* Horloge dans le dashboard (style simple) */
.dashboard-container .digital-clock {
    background: rgba(45, 90, 39, 0.1);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

.digital-clock::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--accent-color), var(--accent-secondary), var(--accent-color));
    border-radius: var(--radius-md);
    z-index: -1;
    animation: border-glow 3s infinite;
}

.clock-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.clock-separator {
    animation: blink 1s infinite;
}

.clock-date {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 0.25rem;
    text-align: center;
}

@keyframes pulse-glow-military {
    0%, 100% {
        box-shadow:
            var(--shadow-military),
            var(--shadow-glow),
            inset 0 0 20px rgba(231, 76, 60, 0.1),
            0 0 5px var(--accent-color);
        transform: translateX(-50%) scale(1);
    }
    50% {
        box-shadow:
            var(--shadow-military),
            var(--shadow-glow),
            inset 0 0 20px rgba(231, 76, 60, 0.2),
            0 0 15px var(--accent-color),
            0 0 25px var(--accent-color);
        transform: translateX(-50%) scale(1.02);
    }
}

@keyframes border-glow {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .digital-clock {
        position: relative;
        top: auto;
        right: auto;
        margin: 1rem auto;
        font-size: 1rem;
    }
}
