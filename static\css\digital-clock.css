/* Horloge Numérique Militaire Marocaine */
.digital-clock {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.95), rgba(192, 57, 43, 0.95));
    border: 3px solid var(--accent-secondary);
    border-radius: var(--radius-lg);
    padding: 1rem 2rem;
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--accent-secondary);
    text-shadow: 0 0 15px var(--accent-secondary);
    box-shadow:
        var(--shadow-military),
        var(--shadow-glow-secondary),
        inset 0 0 30px rgba(243, 156, 18, 0.2);
    backdrop-filter: blur(15px);
    z-index: 1001;
    animation: pulse-glow-moroccan 3s infinite;
    user-select: none;
    text-align: center;
    min-width: 300px;
}

.digital-clock::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--accent-color), var(--accent-secondary), var(--accent-color));
    border-radius: var(--radius-md);
    z-index: -1;
    animation: border-glow 3s infinite;
}

.clock-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.clock-separator {
    animation: blink 1s infinite;
}

.clock-date {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 0.25rem;
    text-align: center;
}

@keyframes pulse-glow-moroccan {
    0%, 100% {
        box-shadow:
            var(--shadow-military),
            var(--shadow-glow-secondary),
            inset 0 0 30px rgba(243, 156, 18, 0.2),
            0 0 10px var(--accent-secondary);
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        box-shadow:
            var(--shadow-military),
            var(--shadow-glow-secondary),
            inset 0 0 30px rgba(243, 156, 18, 0.4),
            0 0 20px var(--accent-secondary),
            0 0 40px var(--accent-secondary);
        transform: translate(-50%, -50%) scale(1.05);
    }
}

@keyframes border-glow {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .digital-clock {
        position: relative;
        top: auto;
        right: auto;
        margin: 1rem auto;
        font-size: 1rem;
    }
}
