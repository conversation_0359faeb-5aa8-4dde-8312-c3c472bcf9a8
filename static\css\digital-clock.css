/* Horloge Numérique Militaire */
.digital-clock {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, rgba(45, 90, 39, 0.95), rgba(26, 61, 26, 0.95));
    border: 2px solid var(--accent-color);
    border-radius: var(--radius-md);
    padding: 0.75rem 1.5rem;
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--accent-color);
    text-shadow: 0 0 10px var(--accent-color);
    box-shadow:
        var(--shadow-military),
        var(--shadow-glow),
        inset 0 0 20px rgba(231, 76, 60, 0.1);
    backdrop-filter: blur(10px);
    z-index: 1001;
    animation: pulse-glow-military 2s infinite;
    user-select: none;
    text-align: center;
    min-width: 250px;
}

.digital-clock::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--accent-color), var(--accent-secondary), var(--accent-color));
    border-radius: var(--radius-md);
    z-index: -1;
    animation: border-glow 3s infinite;
}

.clock-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.clock-separator {
    animation: blink 1s infinite;
}

.clock-date {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 0.25rem;
    text-align: center;
}

@keyframes pulse-glow-military {
    0%, 100% {
        box-shadow:
            var(--shadow-military),
            var(--shadow-glow),
            inset 0 0 20px rgba(231, 76, 60, 0.1),
            0 0 5px var(--accent-color);
        transform: translateX(-50%) scale(1);
    }
    50% {
        box-shadow:
            var(--shadow-military),
            var(--shadow-glow),
            inset 0 0 20px rgba(231, 76, 60, 0.2),
            0 0 15px var(--accent-color),
            0 0 25px var(--accent-color);
        transform: translateX(-50%) scale(1.02);
    }
}

@keyframes border-glow {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .digital-clock {
        position: relative;
        top: auto;
        right: auto;
        margin: 1rem auto;
        font-size: 1rem;
    }
}
