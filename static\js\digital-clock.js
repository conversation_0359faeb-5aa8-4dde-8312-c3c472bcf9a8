/**
 * Horloge Numérique Militaire
 * Affiche l'heure et la date en temps réel avec style militaire
 */

class DigitalClock {
    constructor() {
        this.clockElement = null;
        this.timeElement = null;
        this.dateElement = null;
        this.init();
    }

    init() {
        this.createClockElement();
        this.updateTime();
        this.startClock();
    }

    createClockElement() {
        // Créer l'élément horloge
        this.clockElement = document.createElement('div');
        this.clockElement.className = 'digital-clock';
        this.clockElement.id = 'dashboard-clock';
        this.clockElement.innerHTML = `
            <div class="clock-time">
                <i class="fas fa-clock" style="margin-right: 0.5rem;"></i>
                <span id="clock-time"></span>
            </div>
        `;

        // Récupérer les références
        this.timeElement = document.getElementById('clock-time');

        // L'horloge sera intégrée dans le header du dashboard
        // Ne pas l'ajouter au body ici
    }

    updateTime() {
        const now = new Date();

        // Format de l'heure (24h)
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');

        // Format de la date
        const options = {
            weekday: 'short',
            day: 'numeric',
            month: 'short'
        };
        const dateString = now.toLocaleDateString('fr-FR', options);

        // Mettre à jour l'affichage (simplifié)
        if (this.timeElement) {
            this.timeElement.innerHTML = `${hours}:${minutes} • ${dateString}`;
        }
    }

    startClock() {
        // Mettre à jour chaque seconde
        setInterval(() => {
            this.updateTime();
        }, 1000);
    }

    // Méthodes utilitaires
    show() {
        if (this.clockElement) {
            this.clockElement.style.display = 'block';
        }
    }

    hide() {
        if (this.clockElement) {
            this.clockElement.style.display = 'none';
        }
    }

    toggle() {
        if (this.clockElement) {
            const isVisible = this.clockElement.style.display !== 'none';
            this.clockElement.style.display = isVisible ? 'none' : 'block';
        }
    }

    destroy() {
        if (this.clockElement) {
            this.clockElement.remove();
            this.clockElement = null;
            this.timeElement = null;
            this.dateElement = null;
        }
    }
}

// Fonction pour intégrer l'horloge dans le dashboard
function initDashboardClock() {
    if (window.digitalClock) {
        const clockElement = window.digitalClock.clockElement;
        const dashboardHeader = document.querySelector('.dashboard-header');

        if (dashboardHeader && clockElement) {
            dashboardHeader.appendChild(clockElement);
        }
    }
}

// Initialiser l'horloge au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    // Créer l'horloge seulement si on est sur le dashboard
    if (window.location.pathname.includes('dashboard') || window.location.pathname === '/') {
        window.digitalClock = new DigitalClock();
        // Attendre que le DOM soit complètement chargé
        setTimeout(initDashboardClock, 100);
    }
});

// Gérer le redimensionnement
window.addEventListener('resize', () => {
    if (window.innerWidth <= 768 && window.digitalClock) {
        // Masquer sur mobile
        window.digitalClock.hide();
    } else if (window.innerWidth > 768 && window.digitalClock) {
        // Afficher sur desktop
        window.digitalClock.show();
    } else if (window.innerWidth > 768 && !window.digitalClock) {
        // Créer si n'existe pas sur desktop
        window.digitalClock = new DigitalClock();
    }
});

// Fonctions globales pour contrôler l'horloge
window.toggleClock = function() {
    if (window.digitalClock) {
        window.digitalClock.toggle();
    }
};

window.showClock = function() {
    if (window.digitalClock) {
        window.digitalClock.show();
    }
};

window.hideClock = function() {
    if (window.digitalClock) {
        window.digitalClock.hide();
    }
};
