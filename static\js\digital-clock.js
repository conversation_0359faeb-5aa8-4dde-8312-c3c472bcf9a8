/**
 * Horloge Numérique Militaire
 * Affiche l'heure et la date en temps réel avec style militaire
 */

class DigitalClock {
    constructor() {
        this.clockElement = null;
        this.timeElement = null;
        this.dateElement = null;
        this.init();
    }

    init() {
        this.createClockElement();
        this.updateTime();
        this.startClock();
    }

    createClockElement() {
        // Créer l'élément horloge
        this.clockElement = document.createElement('div');
        this.clockElement.className = 'digital-clock';
        this.clockElement.innerHTML = `
            <div class="clock-time">
                <span id="clock-time"></span>
            </div>
            <div class="clock-date" id="clock-date"></div>
            <div style="margin-top: 0.25rem; font-size: 0.8rem; color: var(--accent-color); font-family: 'Orbitron', monospace; font-weight: 600;">
                SYSTÈME TACTIQUE
            </div>
        `;

        // Ajouter au body
        document.body.appendChild(this.clockElement);

        // Récupérer les références
        this.timeElement = document.getElementById('clock-time');
        this.dateElement = document.getElementById('clock-date');
    }

    updateTime() {
        const now = new Date();
        
        // Format de l'heure (24h)
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const seconds = now.getSeconds().toString().padStart(2, '0');
        
        // Format de la date
        const options = { 
            weekday: 'short', 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric' 
        };
        const dateString = now.toLocaleDateString('fr-FR', options);

        // Mettre à jour l'affichage
        this.timeElement.innerHTML = `
            ${hours}<span class="clock-separator">:</span>${minutes}<span class="clock-separator">:</span>${seconds}
        `;
        this.dateElement.textContent = dateString.toUpperCase();
    }

    startClock() {
        // Mettre à jour chaque seconde
        setInterval(() => {
            this.updateTime();
        }, 1000);
    }

    // Méthodes utilitaires
    show() {
        if (this.clockElement) {
            this.clockElement.style.display = 'block';
        }
    }

    hide() {
        if (this.clockElement) {
            this.clockElement.style.display = 'none';
        }
    }

    toggle() {
        if (this.clockElement) {
            const isVisible = this.clockElement.style.display !== 'none';
            this.clockElement.style.display = isVisible ? 'none' : 'block';
        }
    }

    destroy() {
        if (this.clockElement) {
            this.clockElement.remove();
            this.clockElement = null;
            this.timeElement = null;
            this.dateElement = null;
        }
    }
}

// Initialiser l'horloge au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si on n'est pas sur mobile pour éviter l'encombrement
    if (window.innerWidth > 768) {
        window.digitalClock = new DigitalClock();
    }
});

// Gérer le redimensionnement
window.addEventListener('resize', () => {
    if (window.innerWidth <= 768 && window.digitalClock) {
        // Masquer sur mobile
        window.digitalClock.hide();
    } else if (window.innerWidth > 768 && window.digitalClock) {
        // Afficher sur desktop
        window.digitalClock.show();
    } else if (window.innerWidth > 768 && !window.digitalClock) {
        // Créer si n'existe pas sur desktop
        window.digitalClock = new DigitalClock();
    }
});

// Fonctions globales pour contrôler l'horloge
window.toggleClock = function() {
    if (window.digitalClock) {
        window.digitalClock.toggle();
    }
};

window.showClock = function() {
    if (window.digitalClock) {
        window.digitalClock.show();
    }
};

window.hideClock = function() {
    if (window.digitalClock) {
        window.digitalClock.hide();
    }
};
